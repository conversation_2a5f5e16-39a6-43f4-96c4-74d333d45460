import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Tabs,
  Select,
  DatePicker,
  message
} from 'antd';
import {
  ReloadOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  HistoryOutlined,
  EyeOutlined
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';
import dayjs from 'dayjs';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
const { TabPane } = Tabs;

interface Order {
  id: number;
  country: string;
  proxy_count: number;
  period_days: number;
  proxy_version: string;
  proxy_type: string;
  total_price: number;
  currency: string;
  status: string;
  created_at: string;
  expires_at: string;
  description?: string;
}

interface Transaction {
  id: number;
  type: string;
  amount: number;
  currency: string;
  description: string;
  reference_id: string;
  status: string;
  created_at: string;
}

interface TransactionStats {
  deposit: { USD: number; RUB: number; count: number };
  purchase: { USD: number; RUB: number; count: number };
  refund: { USD: number; RUB: number; count: number };
  withdraw: { USD: number; RUB: number; count: number };
}

const Orders: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState<Order[]>([]);
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [transactionStats, setTransactionStats] = useState<TransactionStats | null>(null);
  const [activeTab, setActiveTab] = useState('orders');
  const [orderFilters, setOrderFilters] = useState({
    status: '',
    country: ''
  });
  const [transactionFilters, setTransactionFilters] = useState({
    type: '',
    status: '',
    period: 'month'
  });
  const { user } = useAuth();

  useEffect(() => {
    if (activeTab === 'orders') {
      loadOrders();
    } else if (activeTab === 'transactions') {
      loadTransactions();
      loadTransactionStats();
    }
  }, [activeTab, orderFilters, transactionFilters]);

  const loadOrders = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (orderFilters.status) params.append('status', orderFilters.status);
      if (orderFilters.country) params.append('country', orderFilters.country);

      const response = await apiService.api.get(`/orders?${params.toString()}`);
      setOrders(response.data.orders);
    } catch (error) {
      console.error('Failed to load orders:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTransactions = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (transactionFilters.type) params.append('type', transactionFilters.type);
      if (transactionFilters.status) params.append('status', transactionFilters.status);

      const response = await apiService.api.get(`/orders/transactions/history?${params.toString()}`);
      setTransactions(response.data.transactions);
    } catch (error) {
      console.error('Failed to load transactions:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadTransactionStats = async () => {
    try {
      const response = await apiService.api.get(`/orders/transactions/stats?period=${transactionFilters.period}`);
      setTransactionStats(response.data.stats);
    } catch (error) {
      console.error('Failed to load transaction stats:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'completed':
        return 'green';
      case 'expired':
      case 'failed':
        return 'red';
      case 'pending':
        return 'orange';
      case 'cancelled':
        return 'gray';
      default:
        return 'default';
    }
  };

  const getTransactionTypeColor = (type: string) => {
    switch (type) {
      case 'deposit':
        return 'green';
      case 'purchase':
        return 'blue';
      case 'refund':
        return 'orange';
      case 'withdraw':
        return 'red';
      default:
        return 'default';
    }
  };

  const orderColumns = [
    {
      title: 'Order ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'Country',
      dataIndex: 'country',
      key: 'country',
      width: 80,
      render: (country: string) => country.toUpperCase(),
    },
    {
      title: 'Proxies',
      dataIndex: 'proxy_count',
      key: 'proxy_count',
      width: 80,
    },
    {
      title: 'Type',
      key: 'type',
      width: 100,
      render: (record: Order) => `${record.proxy_type.toUpperCase()} IPv${record.proxy_version}`,
    },
    {
      title: 'Price',
      key: 'price',
      width: 100,
      render: (record: Order) => `${record.currency} ${record.total_price}`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: 'Expires',
      dataIndex: 'expires_at',
      key: 'expires_at',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD'),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 100,
      render: (record: Order) => (
        <Button
          type="link"
          icon={<EyeOutlined />}
          onClick={() => message.info(`Order ${record.id} details`)}
        >
          Details
        </Button>
      ),
    },
  ];

  const transactionColumns = [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 60,
    },
    {
      title: 'Type',
      dataIndex: 'type',
      key: 'type',
      width: 100,
      render: (type: string) => (
        <Tag color={getTransactionTypeColor(type)}>
          {type.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Amount',
      key: 'amount',
      width: 120,
      render: (record: Transaction) => (
        <span style={{ 
          color: record.type === 'deposit' || record.type === 'refund' ? '#52c41a' : '#ff4d4f' 
        }}>
          {record.type === 'deposit' || record.type === 'refund' ? '+' : '-'}
          {record.currency} {record.amount}
        </span>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      ellipsis: true,
    },
    {
      title: 'Reference',
      dataIndex: 'reference_id',
      key: 'reference_id',
      width: 120,
      ellipsis: true,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Date',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 120,
      render: (date: string) => dayjs(date).format('YYYY-MM-DD HH:mm'),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2} style={{ margin: 0 }}>
            Orders & Transactions
          </Title>
          <Text type="secondary">
            View your order history and transaction records
          </Text>
        </div>
        <Button icon={<ReloadOutlined />} onClick={() => {
          if (activeTab === 'orders') loadOrders();
          else loadTransactions();
        }} loading={loading}>
          Refresh
        </Button>
      </div>

      <Tabs activeKey={activeTab} onChange={setActiveTab}>
        <TabPane tab={
          <span>
            <ShoppingCartOutlined />
            Orders
          </span>
        } key="orders">
          {/* Order Filters */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Space>
              <Select
                placeholder="Filter by status"
                style={{ width: 150 }}
                allowClear
                value={orderFilters.status || undefined}
                onChange={(value) => setOrderFilters({ ...orderFilters, status: value || '' })}
              >
                <Option value="pending">Pending</Option>
                <Option value="active">Active</Option>
                <Option value="expired">Expired</Option>
                <Option value="cancelled">Cancelled</Option>
              </Select>
              
              <Select
                placeholder="Filter by country"
                style={{ width: 150 }}
                allowClear
                value={orderFilters.country || undefined}
                onChange={(value) => setOrderFilters({ ...orderFilters, country: value || '' })}
              >
                <Option value="us">US</Option>
                <Option value="ru">RU</Option>
                <Option value="de">DE</Option>
                <Option value="uk">UK</Option>
              </Select>
            </Space>
          </Card>

          <Card>
            <Table
              columns={orderColumns}
              dataSource={orders}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `Total ${total} orders`,
              }}
            />
          </Card>
        </TabPane>

        <TabPane tab={
          <span>
            <HistoryOutlined />
            Transactions
          </span>
        } key="transactions">
          {/* Transaction Stats */}
          {transactionStats && (
            <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="Total Deposits"
                    value={transactionStats.deposit.USD + transactionStats.deposit.RUB}
                    precision={2}
                    valueStyle={{ color: '#52c41a' }}
                    prefix={<DollarOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="Total Purchases"
                    value={transactionStats.purchase.USD + transactionStats.purchase.RUB}
                    precision={2}
                    valueStyle={{ color: '#1890ff' }}
                    prefix={<ShoppingCartOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="Total Refunds"
                    value={transactionStats.refund.USD + transactionStats.refund.RUB}
                    precision={2}
                    valueStyle={{ color: '#fa8c16' }}
                    prefix={<DollarOutlined />}
                  />
                </Card>
              </Col>
              <Col xs={24} sm={6}>
                <Card>
                  <Statistic
                    title="Current Balance"
                    value={user?.balance || 0}
                    precision={2}
                    suffix={user?.currency}
                    valueStyle={{ color: '#722ed1' }}
                    prefix={<DollarOutlined />}
                  />
                </Card>
              </Col>
            </Row>
          )}

          {/* Transaction Filters */}
          <Card size="small" style={{ marginBottom: 16 }}>
            <Space>
              <Select
                placeholder="Filter by type"
                style={{ width: 150 }}
                allowClear
                value={transactionFilters.type || undefined}
                onChange={(value) => setTransactionFilters({ ...transactionFilters, type: value || '' })}
              >
                <Option value="deposit">Deposit</Option>
                <Option value="purchase">Purchase</Option>
                <Option value="refund">Refund</Option>
                <Option value="withdraw">Withdraw</Option>
              </Select>
              
              <Select
                placeholder="Filter by status"
                style={{ width: 150 }}
                allowClear
                value={transactionFilters.status || undefined}
                onChange={(value) => setTransactionFilters({ ...transactionFilters, status: value || '' })}
              >
                <Option value="pending">Pending</Option>
                <Option value="completed">Completed</Option>
                <Option value="failed">Failed</Option>
                <Option value="cancelled">Cancelled</Option>
              </Select>

              <Select
                placeholder="Period"
                style={{ width: 120 }}
                value={transactionFilters.period}
                onChange={(value) => setTransactionFilters({ ...transactionFilters, period: value })}
              >
                <Option value="today">Today</Option>
                <Option value="week">This Week</Option>
                <Option value="month">This Month</Option>
                <Option value="year">This Year</Option>
              </Select>
            </Space>
          </Card>

          <Card>
            <Table
              columns={transactionColumns}
              dataSource={transactions}
              rowKey="id"
              loading={loading}
              pagination={{
                pageSize: 10,
                showSizeChanger: true,
                showQuickJumper: true,
                showTotal: (total) => `Total ${total} transactions`,
              }}
            />
          </Card>
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Orders;
