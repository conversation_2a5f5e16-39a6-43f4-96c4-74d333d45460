import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Modal,
  Form,
  Input,
  Switch,
  message,
  Popconfirm,
  Tooltip
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  EditOutlined,
  DeleteOutlined,
  EyeOutlined,
  KeyOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined
} from '@ant-design/icons';
import apiService from '../services/api';

const { Title, Text } = Typography;

interface ApiKey {
  id: number;
  key_name: string;
  px6_api_key: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface ApiKeyForm {
  key_name: string;
  px6_api_key: string;
}

const ApiKeys: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [apiKeys, setApiKeys] = useState<ApiKey[]>([]);
  const [modalVisible, setModalVisible] = useState(false);
  const [editingKey, setEditingKey] = useState<ApiKey | null>(null);
  const [testingKey, setTestingKey] = useState<number | null>(null);
  const [form] = Form.useForm();

  useEffect(() => {
    loadApiKeys();
  }, []);

  const loadApiKeys = async () => {
    setLoading(true);
    try {
      const response = await apiService.api.get('/api-keys');
      setApiKeys(response.data.apiKeys);
    } catch (error) {
      console.error('Failed to load API keys:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleAdd = () => {
    setEditingKey(null);
    form.resetFields();
    setModalVisible(true);
  };

  const handleEdit = (key: ApiKey) => {
    setEditingKey(key);
    form.setFieldsValue({
      key_name: key.key_name
    });
    setModalVisible(true);
  };

  const handleSubmit = async (values: ApiKeyForm) => {
    try {
      setLoading(true);
      
      if (editingKey) {
        // 更新API密钥
        await apiService.api.put(`/api-keys/${editingKey.id}`, {
          key_name: values.key_name
        });
        message.success('API key updated successfully');
      } else {
        // 添加新API密钥
        await apiService.api.post('/api-keys', values);
        message.success('API key added successfully');
      }
      
      setModalVisible(false);
      form.resetFields();
      await loadApiKeys();
    } catch (error) {
      console.error('Failed to save API key:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleToggleStatus = async (keyId: number) => {
    try {
      await apiService.api.patch(`/api-keys/${keyId}/toggle`);
      message.success('API key status updated');
      await loadApiKeys();
    } catch (error) {
      console.error('Failed to toggle API key status:', error);
    }
  };

  const handleDelete = async (keyId: number) => {
    try {
      await apiService.api.delete(`/api-keys/${keyId}`);
      message.success('API key deleted successfully');
      await loadApiKeys();
    } catch (error) {
      console.error('Failed to delete API key:', error);
    }
  };

  const handleTest = async (keyId: number) => {
    try {
      setTestingKey(keyId);
      const response = await apiService.api.post(`/api-keys/${keyId}/test`);
      const result = response.data.result;
      
      if (result.valid) {
        message.success(`API key is valid! Balance: ${result.currency} ${result.balance}`);
      } else {
        message.error('API key is invalid');
      }
    } catch (error) {
      console.error('Failed to test API key:', error);
      message.error('Failed to test API key');
    } finally {
      setTestingKey(null);
    }
  };

  const columns = [
    {
      title: 'Name',
      dataIndex: 'key_name',
      key: 'key_name',
      width: 200,
    },
    {
      title: 'API Key',
      dataIndex: 'px6_api_key',
      key: 'px6_api_key',
      width: 300,
      render: (key: string) => (
        <Text code style={{ fontSize: '12px' }}>
          {key}
        </Text>
      ),
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      width: 100,
      render: (active: boolean) => (
        <Tag color={active ? 'green' : 'red'} icon={active ? <CheckCircleOutlined /> : <CloseCircleOutlined />}>
          {active ? 'ACTIVE' : 'INACTIVE'}
        </Tag>
      ),
    },
    {
      title: 'Created',
      dataIndex: 'created_at',
      key: 'created_at',
      width: 150,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 250,
      render: (record: ApiKey) => (
        <Space>
          <Tooltip title="Test API Key">
            <Button
              type="link"
              icon={<EyeOutlined />}
              onClick={() => handleTest(record.id)}
              loading={testingKey === record.id}
            >
              Test
            </Button>
          </Tooltip>
          
          <Tooltip title="Edit">
            <Button
              type="link"
              icon={<EditOutlined />}
              onClick={() => handleEdit(record)}
            >
              Edit
            </Button>
          </Tooltip>
          
          <Tooltip title={record.is_active ? 'Deactivate' : 'Activate'}>
            <Button
              type="link"
              onClick={() => handleToggleStatus(record.id)}
              style={{ color: record.is_active ? '#ff4d4f' : '#52c41a' }}
            >
              {record.is_active ? 'Deactivate' : 'Activate'}
            </Button>
          </Tooltip>
          
          <Popconfirm
            title="Are you sure you want to delete this API key?"
            onConfirm={() => handleDelete(record.id)}
            okText="Yes"
            cancelText="No"
          >
            <Button
              type="link"
              danger
              icon={<DeleteOutlined />}
            >
              Delete
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2} style={{ margin: 0 }}>
            API Keys Management
          </Title>
          <Text type="secondary">
            Manage your PX6.me API keys for proxy operations
          </Text>
        </div>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={loadApiKeys} loading={loading}>
            Refresh
          </Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleAdd}>
            Add API Key
          </Button>
        </Space>
      </div>

      <Card>
        <Table
          columns={columns}
          dataSource={apiKeys}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} API keys`,
          }}
        />
      </Card>

      {/* Add/Edit Modal */}
      <Modal
        title={editingKey ? 'Edit API Key' : 'Add API Key'}
        open={modalVisible}
        onCancel={() => {
          setModalVisible(false);
          form.resetFields();
        }}
        footer={null}
        width={500}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
        >
          <Form.Item
            name="key_name"
            label="Key Name"
            rules={[
              { required: true, message: 'Please enter a name for this API key' },
              { max: 100, message: 'Name must be less than 100 characters' }
            ]}
          >
            <Input placeholder="e.g., My PX6 API Key" />
          </Form.Item>

          {!editingKey && (
            <Form.Item
              name="px6_api_key"
              label="PX6.me API Key"
              rules={[
                { required: true, message: 'Please enter your PX6.me API key' },
                { min: 10, message: 'API key must be at least 10 characters' }
              ]}
            >
              <Input.Password 
                placeholder="Enter your PX6.me API key"
                visibilityToggle={{
                  visibleIcon: <EyeOutlined />,
                  hiddenIcon: <EyeOutlined />
                }}
              />
            </Form.Item>
          )}

          <div style={{ marginBottom: 16 }}>
            <Text type="secondary">
              <KeyOutlined style={{ marginRight: 8 }} />
              You can get your API key from your PX6.me account dashboard.
            </Text>
          </div>

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                {editingKey ? 'Update' : 'Add'} API Key
              </Button>
              <Button onClick={() => setModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  );
};

export default ApiKeys;
