import React, { useState, useEffect } from 'react';
import {
  Row,
  Col,
  Card,
  Statistic,
  Typography,
  Space,
  Button,
  Alert,
  Spin,
  Progress
} from 'antd';
import {
  GlobalOutlined,
  ShoppingCartOutlined,
  WalletOutlined,
  UserOutlined,
  PlusOutlined,
  ReloadOutlined
} from '@ant-design/icons';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';

const { Title, Text } = Typography;

interface DashboardStats {
  totalProxies: number;
  activeProxies: number;
  totalOrders: number;
  balance: number;
  currency: string;
}

const Dashboard: React.FC = () => {
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState<DashboardStats>({
    totalProxies: 0,
    activeProxies: 0,
    totalOrders: 0,
    balance: 0,
    currency: 'USD'
  });
  const [healthStatus, setHealthStatus] = useState<any>(null);
  const { user, isAdmin } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    loadDashboardData();
  }, []);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // 加载健康检查
      const health = await apiService.healthCheck();
      setHealthStatus(health);

      // 模拟统计数据（实际应该从API获取）
      setStats({
        totalProxies: 0,
        activeProxies: 0,
        totalOrders: 0,
        balance: user?.balance || 0,
        currency: user?.currency || 'USD'
      });
    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleRefresh = () => {
    loadDashboardData();
  };

  const getHealthStatusColor = () => {
    if (!healthStatus) return 'default';
    return healthStatus.status === 'ok' ? 'success' : 'error';
  };

  const getHealthStatusText = () => {
    if (!healthStatus) return 'Unknown';
    return healthStatus.status === 'ok' ? 'System Online' : 'System Error';
  };

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
      </div>
    );
  }

  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2} style={{ margin: 0 }}>
            Dashboard
          </Title>
          <Text type="secondary">
            Welcome back, {user?.username}! Here's your proxy system overview.
          </Text>
        </div>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={handleRefresh}>
            Refresh
          </Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={() => navigate('/proxies')}>
            Buy Proxies
          </Button>
        </Space>
      </div>

      {/* System Status Alert */}
      <Alert
        message={getHealthStatusText()}
        description={`Database: ${healthStatus?.database || 'Unknown'} | Last updated: ${healthStatus?.timestamp || 'Unknown'}`}
        type={getHealthStatusColor() as any}
        showIcon
        style={{ marginBottom: 24 }}
      />

      {/* Statistics Cards */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Proxies"
              value={stats.totalProxies}
              prefix={<GlobalOutlined style={{ color: '#1890ff' }} />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Active Proxies"
              value={stats.activeProxies}
              prefix={<GlobalOutlined style={{ color: '#52c41a' }} />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Total Orders"
              value={stats.totalOrders}
              prefix={<ShoppingCartOutlined style={{ color: '#722ed1' }} />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={12} lg={6}>
          <Card>
            <Statistic
              title="Balance"
              value={stats.balance}
              precision={2}
              prefix={<WalletOutlined style={{ color: '#fa8c16' }} />}
              suffix={stats.currency}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      <Row gutter={[16, 16]}>
        {/* Quick Actions */}
        <Col xs={24} lg={12}>
          <Card title="Quick Actions" extra={<Button type="link">View All</Button>}>
            <Space direction="vertical" style={{ width: '100%' }} size="middle">
              <Button
                type="default"
                icon={<GlobalOutlined />}
                block
                onClick={() => navigate('/proxies')}
              >
                Buy New Proxies
              </Button>
              <Button
                type="default"
                icon={<ShoppingCartOutlined />}
                block
                onClick={() => navigate('/orders')}
              >
                View Orders
              </Button>
              <Button
                type="default"
                icon={<WalletOutlined />}
                block
                onClick={() => navigate('/profile')}
              >
                Manage Balance
              </Button>
              {isAdmin && (
                <Button
                  type="default"
                  icon={<UserOutlined />}
                  block
                  onClick={() => navigate('/admin/users')}
                >
                  Manage Users
                </Button>
              )}
            </Space>
          </Card>
        </Col>

        {/* Recent Activity */}
        <Col xs={24} lg={12}>
          <Card title="Recent Activity" extra={<Button type="link">View All</Button>}>
            <div style={{ textAlign: 'center', padding: '40px 0', color: '#999' }}>
              <GlobalOutlined style={{ fontSize: 48, marginBottom: 16 }} />
              <div>No recent activity</div>
              <Text type="secondary">Your recent proxy activities will appear here</Text>
            </div>
          </Card>
        </Col>
      </Row>

      {/* Usage Overview */}
      <Row gutter={[16, 16]} style={{ marginTop: 16 }}>
        <Col span={24}>
          <Card title="Usage Overview">
            <Row gutter={[16, 16]}>
              <Col xs={24} md={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={0}
                    format={() => '0/0'}
                    strokeColor="#1890ff"
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text strong>Active Proxies</Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} md={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={0}
                    format={() => `${stats.currency} 0`}
                    strokeColor="#52c41a"
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text strong>Monthly Spending</Text>
                  </div>
                </div>
              </Col>
              <Col xs={24} md={8}>
                <div style={{ textAlign: 'center' }}>
                  <Progress
                    type="circle"
                    percent={100}
                    format={() => '100%'}
                    strokeColor="#fa8c16"
                  />
                  <div style={{ marginTop: 8 }}>
                    <Text strong>System Uptime</Text>
                  </div>
                </div>
              </Col>
            </Row>
          </Card>
        </Col>
      </Row>
    </div>
  );
};

export default Dashboard;
