import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Space,
  Tag,
  Typography,
  Row,
  Col,
  Statistic,
  Modal,
  Form,
  Input,
  Select,
  InputNumber,
  Switch,
  message,
  Tooltip,
  Divider
} from 'antd';
import {
  PlusOutlined,
  ReloadOutlined,
  GlobalOutlined,
  EyeOutlined,
  ShoppingCartOutlined,
  InfoCircleOutlined
} from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';
import apiService from '../services/api';

const { Title, Text } = Typography;
const { Option } = Select;

interface ProxyOrder {
  id: number;
  country: string;
  proxy_count: number;
  period_days: number;
  proxy_version: string;
  proxy_type: string;
  total_price: number;
  currency: string;
  status: string;
  created_at: string;
  expires_at: string;
  proxies: ProxyDetail[];
}

interface ProxyDetail {
  id: number;
  px6_proxy_id: string;
  ip_address: string;
  host: string;
  port: number;
  username: string;
  password: string;
  proxy_type: string;
  country: string;
  is_active: boolean;
  date_end: string;
}

interface BuyProxyForm {
  count: number;
  period: number;
  country: string;
  version: string;
  type: string;
  api_key_id: number;
  description: string;
  auto_prolong: boolean;
}

const Proxies: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [orders, setOrders] = useState<ProxyOrder[]>([]);
  const [countries, setCountries] = useState<string[]>([]);
  const [apiKeys, setApiKeys] = useState<any[]>([]);
  const [buyModalVisible, setBuyModalVisible] = useState(false);
  const [detailModalVisible, setDetailModalVisible] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<ProxyOrder | null>(null);
  const [priceInfo, setPriceInfo] = useState<any>(null);
  const [form] = Form.useForm();
  const { user } = useAuth();

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      await Promise.all([
        loadOrders(),
        loadCountries(),
        loadApiKeys()
      ]);
    } catch (error) {
      console.error('Failed to load data:', error);
    } finally {
      setLoading(false);
    }
  };

  const loadOrders = async () => {
    try {
      const response = await apiService.api.get('/proxies/my-proxies');
      setOrders(response.data.orders);
    } catch (error) {
      console.error('Failed to load orders:', error);
    }
  };

  const loadCountries = async () => {
    try {
      const response = await apiService.getProxyCountries();
      setCountries(response.countries);
    } catch (error) {
      console.error('Failed to load countries:', error);
    }
  };

  const loadApiKeys = async () => {
    try {
      const response = await apiService.api.get('/api-keys');
      setApiKeys(response.data.apiKeys.filter((key: any) => key.is_active));
    } catch (error) {
      console.error('Failed to load API keys:', error);
    }
  };

  const handleBuyProxy = () => {
    if (apiKeys.length === 0) {
      message.warning('Please add an API key first');
      return;
    }
    setBuyModalVisible(true);
  };

  const handleFormValuesChange = async (changedValues: any, allValues: any) => {
    if (changedValues.count || changedValues.period || changedValues.version) {
      const { count, period, version } = allValues;
      if (count && period) {
        try {
          const price = await apiService.getProxyPrice(count, period, version);
          setPriceInfo(price);
        } catch (error) {
          setPriceInfo(null);
        }
      }
    }
  };

  const handleBuySubmit = async (values: BuyProxyForm) => {
    try {
      setLoading(true);
      const response = await apiService.api.post('/proxies/buy', values);
      message.success(response.data.message);
      setBuyModalVisible(false);
      form.resetFields();
      setPriceInfo(null);
      await loadOrders();
    } catch (error) {
      console.error('Failed to buy proxies:', error);
    } finally {
      setLoading(false);
    }
  };

  const showOrderDetails = (order: ProxyOrder) => {
    setSelectedOrder(order);
    setDetailModalVisible(true);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'expired': return 'red';
      case 'pending': return 'orange';
      case 'cancelled': return 'gray';
      default: return 'default';
    }
  };

  const columns = [
    {
      title: 'Order ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
    },
    {
      title: 'Country',
      dataIndex: 'country',
      key: 'country',
      width: 80,
      render: (country: string) => country.toUpperCase(),
    },
    {
      title: 'Count',
      dataIndex: 'proxy_count',
      key: 'proxy_count',
      width: 80,
    },
    {
      title: 'Type',
      dataIndex: 'proxy_type',
      key: 'proxy_type',
      width: 80,
      render: (type: string) => type.toUpperCase(),
    },
    {
      title: 'Version',
      dataIndex: 'proxy_version',
      key: 'proxy_version',
      width: 80,
      render: (version: string) => `IPv${version}`,
    },
    {
      title: 'Price',
      key: 'price',
      width: 100,
      render: (record: ProxyOrder) => `${record.currency} ${record.total_price}`,
    },
    {
      title: 'Status',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      render: (status: string) => (
        <Tag color={getStatusColor(status)}>
          {status.toUpperCase()}
        </Tag>
      ),
    },
    {
      title: 'Expires',
      dataIndex: 'expires_at',
      key: 'expires_at',
      width: 120,
      render: (date: string) => new Date(date).toLocaleDateString(),
    },
    {
      title: 'Actions',
      key: 'actions',
      width: 120,
      render: (record: ProxyOrder) => (
        <Space>
          <Button
            type="link"
            icon={<EyeOutlined />}
            onClick={() => showOrderDetails(record)}
          >
            Details
          </Button>
        </Space>
      ),
    },
  ];

  const proxyDetailColumns = [
    {
      title: 'Host:Port',
      key: 'endpoint',
      render: (record: ProxyDetail) => `${record.host}:${record.port}`,
    },
    {
      title: 'Username',
      dataIndex: 'username',
      key: 'username',
    },
    {
      title: 'Password',
      dataIndex: 'password',
      key: 'password',
    },
    {
      title: 'Type',
      dataIndex: 'proxy_type',
      key: 'proxy_type',
      render: (type: string) => type.toUpperCase(),
    },
    {
      title: 'Status',
      dataIndex: 'is_active',
      key: 'is_active',
      render: (active: boolean) => (
        <Tag color={active ? 'green' : 'red'}>
          {active ? 'ACTIVE' : 'INACTIVE'}
        </Tag>
      ),
    },
  ];

  const activeOrders = orders.filter(order => order.status === 'active');
  const totalProxies = orders.reduce((sum, order) => sum + order.proxy_count, 0);
  const activeProxies = activeOrders.reduce((sum, order) => sum + order.proxy_count, 0);

  return (
    <div>
      <div style={{ marginBottom: 24, display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div>
          <Title level={2} style={{ margin: 0 }}>
            Proxy Management
          </Title>
          <Text type="secondary">
            Manage your proxy orders and view proxy details
          </Text>
        </div>
        <Space>
          <Button icon={<ReloadOutlined />} onClick={loadData} loading={loading}>
            Refresh
          </Button>
          <Button type="primary" icon={<PlusOutlined />} onClick={handleBuyProxy}>
            Buy Proxies
          </Button>
        </Space>
      </div>

      {/* Statistics */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Total Proxies"
              value={totalProxies}
              prefix={<GlobalOutlined style={{ color: '#1890ff' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Active Proxies"
              value={activeProxies}
              prefix={<GlobalOutlined style={{ color: '#52c41a' }} />}
            />
          </Card>
        </Col>
        <Col xs={24} sm={8}>
          <Card>
            <Statistic
              title="Total Orders"
              value={orders.length}
              prefix={<ShoppingCartOutlined style={{ color: '#722ed1' }} />}
            />
          </Card>
        </Col>
      </Row>

      {/* Orders Table */}
      <Card title="Proxy Orders">
        <Table
          columns={columns}
          dataSource={orders}
          rowKey="id"
          loading={loading}
          pagination={{
            pageSize: 10,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `Total ${total} orders`,
          }}
        />
      </Card>

      {/* Buy Proxy Modal */}
      <Modal
        title="Buy Proxies"
        open={buyModalVisible}
        onCancel={() => {
          setBuyModalVisible(false);
          form.resetFields();
          setPriceInfo(null);
        }}
        footer={null}
        width={600}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleBuySubmit}
          onValuesChange={handleFormValuesChange}
          initialValues={{
            version: '6',
            type: 'http',
            auto_prolong: false
          }}
        >
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="count"
                label="Proxy Count"
                rules={[{ required: true, message: 'Please enter proxy count' }]}
              >
                <InputNumber min={1} max={1000} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="period"
                label="Period (Days)"
                rules={[{ required: true, message: 'Please enter period' }]}
              >
                <InputNumber min={1} max={365} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="country"
                label="Country"
                rules={[{ required: true, message: 'Please select country' }]}
              >
                <Select placeholder="Select country">
                  {countries.map(country => (
                    <Option key={country} value={country}>
                      {country.toUpperCase()}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="api_key_id"
                label="API Key"
                rules={[{ required: true, message: 'Please select API key' }]}
              >
                <Select placeholder="Select API key">
                  {apiKeys.map(key => (
                    <Option key={key.id} value={key.id}>
                      {key.key_name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item name="version" label="IP Version">
                <Select>
                  <Option value="6">IPv6</Option>
                  <Option value="4">IPv4</Option>
                  <Option value="3">IPv4 Shared</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item name="type" label="Proxy Type">
                <Select>
                  <Option value="http">HTTP/HTTPS</Option>
                  <Option value="socks">SOCKS5</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item name="description" label="Description (Optional)">
            <Input placeholder="Technical comment" maxLength={50} />
          </Form.Item>

          <Form.Item name="auto_prolong" valuePropName="checked">
            <Switch /> Auto-renewal
          </Form.Item>

          {priceInfo && (
            <Card size="small" style={{ marginBottom: 16, backgroundColor: '#f6ffed' }}>
              <Row gutter={16}>
                <Col span={12}>
                  <Statistic
                    title="Total Price"
                    value={priceInfo.total_price}
                    suffix={priceInfo.currency}
                    precision={2}
                  />
                </Col>
                <Col span={12}>
                  <Statistic
                    title="Price per Proxy"
                    value={priceInfo.price_per_proxy}
                    suffix={priceInfo.currency}
                    precision={2}
                  />
                </Col>
              </Row>
            </Card>
          )}

          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit" loading={loading}>
                Buy Proxies
              </Button>
              <Button onClick={() => setBuyModalVisible(false)}>
                Cancel
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>

      {/* Order Details Modal */}
      <Modal
        title={`Order #${selectedOrder?.id} Details`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedOrder && (
          <div>
            <Row gutter={16} style={{ marginBottom: 16 }}>
              <Col span={8}>
                <Statistic title="Country" value={selectedOrder.country.toUpperCase()} />
              </Col>
              <Col span={8}>
                <Statistic title="Proxy Count" value={selectedOrder.proxy_count} />
              </Col>
              <Col span={8}>
                <Statistic
                  title="Total Price"
                  value={selectedOrder.total_price}
                  suffix={selectedOrder.currency}
                  precision={2}
                />
              </Col>
            </Row>

            <Divider />

            <Table
              columns={proxyDetailColumns}
              dataSource={selectedOrder.proxies}
              rowKey="id"
              pagination={false}
              size="small"
              scroll={{ x: 600 }}
            />
          </div>
        )}
      </Modal>
    </div>
  );
};

export default Proxies;
