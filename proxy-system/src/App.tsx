import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { ConfigProvider } from 'antd';
import { AuthProvider } from './contexts/AuthContext';
import ProtectedRoute from './components/ProtectedRoute';
import Layout from './components/Layout';
import Login from './pages/Login';
import Dashboard from './pages/Dashboard';
import './App.css';

// 临时占位组件
const Proxies = () => <div>Proxies Page - Coming Soon</div>;
const Orders = () => <div>Orders Page - Coming Soon</div>;
const ApiKeys = () => <div>API Keys Page - Coming Soon</div>;
const Profile = () => <div>Profile Page - Coming Soon</div>;
const Settings = () => <div>Settings Page - Coming Soon</div>;
const AdminUsers = () => <div>Admin Users Page - Coming Soon</div>;
const AdminSystem = () => <div>Admin System Page - Coming Soon</div>;

function App() {
  return (
    <ConfigProvider
      theme={{
        token: {
          colorPrimary: '#1890ff',
          borderRadius: 6,
        },
      }}
    >
      <AuthProvider>
        <Router>
          <Routes>
            {/* 公开路由 */}
            <Route path="/login" element={<Login />} />

            {/* 受保护的路由 */}
            <Route path="/" element={
              <ProtectedRoute>
                <Layout />
              </ProtectedRoute>
            }>
              <Route index element={<Navigate to="/dashboard" replace />} />
              <Route path="dashboard" element={<Dashboard />} />
              <Route path="proxies" element={<Proxies />} />
              <Route path="orders" element={<Orders />} />
              <Route path="api-keys" element={<ApiKeys />} />
              <Route path="profile" element={<Profile />} />
              <Route path="settings" element={<Settings />} />

              {/* 管理员路由 */}
              <Route path="admin/users" element={
                <ProtectedRoute requireAdmin>
                  <AdminUsers />
                </ProtectedRoute>
              } />
              <Route path="admin/system" element={
                <ProtectedRoute requireAdmin>
                  <AdminSystem />
                </ProtectedRoute>
              } />
            </Route>

            {/* 404 重定向 */}
            <Route path="*" element={<Navigate to="/dashboard" replace />} />
          </Routes>
        </Router>
      </AuthProvider>
    </ConfigProvider>
  );
}

export default App;
