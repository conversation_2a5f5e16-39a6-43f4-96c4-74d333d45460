import express from 'express';
import { query, validationResult } from 'express-validator';
import { authenticateToken, AuthRequest, requireAdmin } from '../middleware/auth';
import { ProxyOrder, ProxyDetail } from '../models/ProxyOrder';
import { Transaction } from '../models/Transaction';

const router = express.Router();

// 获取用户订单列表
router.get('/', authenticateToken, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['pending', 'active', 'expired', 'cancelled']).withMessage('Invalid status'),
  query('country').optional().isLength({ min: 2, max: 2 }).withMessage('Invalid country code')
], async (req: AuthRequest, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const filters = {
      status: req.query.status as string,
      country: req.query.country as string,
      proxy_type: req.query.type as string
    };

    const { orders, total } = await ProxyOrder.findByUserId(req.user.id, page, limit, filters);

    res.json({
      orders,
      pagination: {
        current_page: page,
        per_page: limit,
        total: total,
        total_pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get orders error:', error);
    res.status(500).json({
      error: 'Failed to get orders',
      message: 'Internal server error'
    });
  }
});

// 获取单个订单详情
router.get('/:orderId', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const orderId = parseInt(req.params.orderId);
    const order = await ProxyOrder.findById(orderId);

    if (!order) {
      return res.status(404).json({
        error: 'Order not found',
        message: 'The specified order does not exist'
      });
    }

    // 验证订单是否属于当前用户（非管理员）
    if (req.user.role !== 'admin' && order.user_id !== req.user.id) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only access your own orders'
      });
    }

    // 获取代理详情
    const proxies = await ProxyDetail.findByOrderId(orderId);

    res.json({
      order: {
        ...order,
        proxies: proxies
      }
    });

  } catch (error) {
    console.error('Get order details error:', error);
    res.status(500).json({
      error: 'Failed to get order details',
      message: 'Internal server error'
    });
  }
});

// 获取用户交易记录
router.get('/transactions/history', authenticateToken, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('type').optional().isIn(['deposit', 'withdraw', 'purchase', 'refund']).withMessage('Invalid transaction type'),
  query('status').optional().isIn(['pending', 'completed', 'failed', 'cancelled']).withMessage('Invalid status')
], async (req: AuthRequest, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const filters = {
      type: req.query.type as string,
      status: req.query.status as string,
      start_date: req.query.start_date as string,
      end_date: req.query.end_date as string
    };

    const { transactions, total } = await Transaction.findByUserId(req.user.id, page, limit, filters);

    res.json({
      transactions,
      pagination: {
        current_page: page,
        per_page: limit,
        total: total,
        total_pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get transactions error:', error);
    res.status(500).json({
      error: 'Failed to get transactions',
      message: 'Internal server error'
    });
  }
});

// 获取用户交易统计
router.get('/transactions/stats', authenticateToken, [
  query('period').optional().isIn(['today', 'week', 'month', 'year']).withMessage('Invalid period')
], async (req: AuthRequest, res) => {
  try {
    const period = req.query.period as string;
    const stats = await Transaction.getUserStats(req.user.id, period);

    // 格式化统计数据
    const formattedStats = {
      deposit: { USD: 0, RUB: 0, count: 0 },
      purchase: { USD: 0, RUB: 0, count: 0 },
      refund: { USD: 0, RUB: 0, count: 0 },
      withdraw: { USD: 0, RUB: 0, count: 0 }
    };

    stats.forEach((stat: any) => {
      if (formattedStats[stat.type as keyof typeof formattedStats]) {
        formattedStats[stat.type as keyof typeof formattedStats][stat.currency as 'USD' | 'RUB'] = stat.total_amount;
        formattedStats[stat.type as keyof typeof formattedStats].count += stat.count;
      }
    });

    res.json({
      period: period || 'all_time',
      stats: formattedStats
    });

  } catch (error) {
    console.error('Get transaction stats error:', error);
    res.status(500).json({
      error: 'Failed to get transaction stats',
      message: 'Internal server error'
    });
  }
});

// 获取所有订单（管理员）
router.get('/admin/all', authenticateToken, requireAdmin, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('user_id').optional().isInt({ min: 1 }).withMessage('Invalid user ID')
], async (req: AuthRequest, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;

    // TODO: 实现管理员获取所有订单的逻辑
    res.json({
      message: 'Admin orders endpoint - to be implemented',
      orders: [],
      pagination: {
        current_page: page,
        per_page: limit,
        total: 0,
        total_pages: 0
      }
    });

  } catch (error) {
    console.error('Get admin orders error:', error);
    res.status(500).json({
      error: 'Failed to get orders',
      message: 'Internal server error'
    });
  }
});

export default router;
