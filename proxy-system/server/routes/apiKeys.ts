import express from 'express';
import { body, validationResult } from 'express-validator';
import { authenticateToken, AuthRequest, requireOwnership } from '../middleware/auth';
import { ApiKey } from '../models/ApiKey';
import { PX6Service } from '../services/PX6Service';

const router = express.Router();

// 获取用户的API密钥列表
router.get('/', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const apiKeys = await ApiKey.findByUserId(req.user.id);

    // 隐藏完整的API密钥，只显示部分字符
    const maskedKeys = apiKeys.map(key => ({
      ...key,
      px6_api_key: key.px6_api_key.substring(0, 8) + '...' + key.px6_api_key.substring(key.px6_api_key.length - 4)
    }));

    res.json({
      apiKeys: maskedKeys
    });
  } catch (error) {
    console.error('Get API keys error:', error);
    res.status(500).json({
      error: 'Failed to get API keys',
      message: 'Internal server error'
    });
  }
});

// 添加新的API密钥
router.post('/', authenticateToken, [
  body('key_name').isLength({ min: 1, max: 100 }).withMessage('Key name is required and must be less than 100 characters'),
  body('px6_api_key').isLength({ min: 10 }).withMessage('PX6 API key is required and must be at least 10 characters')
], async (req: AuthRequest, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { key_name, px6_api_key } = req.body;

    // 验证API密钥是否有效
    try {
      const px6Service = new PX6Service(px6_api_key);
      const testResult = await px6Service.testConnection();

      if (testResult.status !== 'yes') {
        return res.status(400).json({
          error: 'Invalid API key',
          message: 'The provided PX6 API key is not valid'
        });
      }
    } catch (error) {
      return res.status(400).json({
        error: 'Invalid API key',
        message: 'Failed to validate the PX6 API key'
      });
    }

    // 创建API密钥记录
    const apiKey = await ApiKey.create({
      user_id: req.user.id,
      key_name,
      px6_api_key,
      is_active: true
    });

    // 返回时隐藏完整的API密钥
    const maskedKey = {
      ...apiKey,
      px6_api_key: px6_api_key.substring(0, 8) + '...' + px6_api_key.substring(px6_api_key.length - 4)
    };

    res.status(201).json({
      message: 'API key added successfully',
      apiKey: maskedKey
    });

  } catch (error) {
    console.error('Add API key error:', error);
    res.status(500).json({
      error: 'Failed to add API key',
      message: 'Internal server error'
    });
  }
});

// 更新API密钥
router.put('/:keyId', authenticateToken, [
  body('key_name').optional().isLength({ min: 1, max: 100 }).withMessage('Key name must be less than 100 characters'),
  body('is_active').optional().isBoolean().withMessage('is_active must be a boolean')
], async (req: AuthRequest, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const keyId = parseInt(req.params.keyId);

    // 验证API密钥是否属于当前用户
    const ownership = await ApiKey.verifyOwnership(keyId, req.user.id);
    if (!ownership) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only modify your own API keys'
      });
    }

    const updateData = {
      key_name: req.body.key_name,
      is_active: req.body.is_active
    };

    // 移除undefined值
    Object.keys(updateData).forEach(key => {
      if (updateData[key as keyof typeof updateData] === undefined) {
        delete updateData[key as keyof typeof updateData];
      }
    });

    const updatedKey = await ApiKey.update(keyId, updateData);

    if (!updatedKey) {
      return res.status(404).json({
        error: 'API key not found',
        message: 'The specified API key does not exist'
      });
    }

    // 隐藏完整的API密钥
    const maskedKey = {
      ...updatedKey,
      px6_api_key: updatedKey.px6_api_key.substring(0, 8) + '...' + updatedKey.px6_api_key.substring(updatedKey.px6_api_key.length - 4)
    };

    res.json({
      message: 'API key updated successfully',
      apiKey: maskedKey
    });

  } catch (error) {
    console.error('Update API key error:', error);
    res.status(500).json({
      error: 'Failed to update API key',
      message: 'Internal server error'
    });
  }
});

// 切换API密钥状态
router.patch('/:keyId/toggle', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const keyId = parseInt(req.params.keyId);

    // 验证API密钥是否属于当前用户
    const ownership = await ApiKey.verifyOwnership(keyId, req.user.id);
    if (!ownership) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only modify your own API keys'
      });
    }

    const success = await ApiKey.toggleStatus(keyId);

    if (!success) {
      return res.status(404).json({
        error: 'API key not found',
        message: 'The specified API key does not exist'
      });
    }

    const updatedKey = await ApiKey.findById(keyId);

    res.json({
      message: 'API key status updated successfully',
      apiKey: {
        id: updatedKey?.id,
        key_name: updatedKey?.key_name,
        is_active: updatedKey?.is_active
      }
    });

  } catch (error) {
    console.error('Toggle API key status error:', error);
    res.status(500).json({
      error: 'Failed to toggle API key status',
      message: 'Internal server error'
    });
  }
});

// 删除API密钥
router.delete('/:keyId', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const keyId = parseInt(req.params.keyId);

    // 验证API密钥是否属于当前用户
    const ownership = await ApiKey.verifyOwnership(keyId, req.user.id);
    if (!ownership) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only delete your own API keys'
      });
    }

    const success = await ApiKey.delete(keyId);

    if (!success) {
      return res.status(404).json({
        error: 'API key not found',
        message: 'The specified API key does not exist'
      });
    }

    res.json({
      message: 'API key deleted successfully'
    });

  } catch (error) {
    console.error('Delete API key error:', error);
    res.status(500).json({
      error: 'Failed to delete API key',
      message: 'Internal server error'
    });
  }
});

// 测试API密钥
router.post('/:keyId/test', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const keyId = parseInt(req.params.keyId);

    // 验证API密钥是否属于当前用户
    const ownership = await ApiKey.verifyOwnership(keyId, req.user.id);
    if (!ownership) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only test your own API keys'
      });
    }

    const apiKey = await ApiKey.findById(keyId);
    if (!apiKey) {
      return res.status(404).json({
        error: 'API key not found',
        message: 'The specified API key does not exist'
      });
    }

    // 测试API密钥
    const px6Service = new PX6Service(apiKey.px6_api_key);
    const testResult = await px6Service.testConnection();

    res.json({
      message: 'API key test completed',
      result: {
        status: testResult.status,
        user_id: testResult.user_id,
        balance: testResult.balance,
        currency: testResult.currency,
        valid: testResult.status === 'yes'
      }
    });

  } catch (error) {
    console.error('Test API key error:', error);
    res.status(500).json({
      error: 'Failed to test API key',
      message: error instanceof Error ? error.message : 'Internal server error'
    });
  }
});

export default router;
