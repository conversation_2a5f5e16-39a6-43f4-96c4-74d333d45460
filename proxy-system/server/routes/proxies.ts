import express from 'express';
import { body, query, validationResult } from 'express-validator';
import { authenticateToken, AuthRequest, requireOwnership } from '../middleware/auth';
import { PX6Service } from '../services/PX6Service';
import { ProxyOrder, ProxyDetail } from '../models/ProxyOrder';
import { ApiKey } from '../models/ApiKey';
import { User } from '../models/User';
import database from '../config/database';

const router = express.Router();

// 获取可用国家列表
router.get('/countries', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const { version = '6' } = req.query;
    
    // 这里应该从用户的API密钥获取，暂时使用默认密钥
    const apiKey = process.env.PX6_DEFAULT_API_KEY;
    if (!apiKey) {
      return res.status(500).json({
        error: 'API key not configured',
        message: 'PX6 API key is not configured'
      });
    }

    const px6Service = new PX6Service(apiKey);
    const result = await px6Service.getCountries(version as '4' | '3' | '6');

    res.json({
      countries: result.list || [],
      version
    });
  } catch (error) {
    console.error('Get countries error:', error);
    res.status(500).json({
      error: 'Failed to get countries',
      message: error instanceof Error ? error.message : 'Internal server error'
    });
  }
});

// 获取指定国家的可用代理数量
router.get('/count', authenticateToken, [
  query('country').notEmpty().withMessage('Country is required'),
  query('version').optional().isIn(['4', '3', '6']).withMessage('Invalid version')
], async (req: AuthRequest, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { country, version = '6' } = req.query;
    
    const apiKey = process.env.PX6_DEFAULT_API_KEY;
    if (!apiKey) {
      return res.status(500).json({
        error: 'API key not configured',
        message: 'PX6 API key is not configured'
      });
    }

    const px6Service = new PX6Service(apiKey);
    const result = await px6Service.getCount(country as string, version as '4' | '3' | '6');

    res.json({
      country,
      version,
      available_count: result.count || 0
    });
  } catch (error) {
    console.error('Get proxy count error:', error);
    res.status(500).json({
      error: 'Failed to get proxy count',
      message: error instanceof Error ? error.message : 'Internal server error'
    });
  }
});

// 获取价格信息
router.get('/price', authenticateToken, [
  query('count').isInt({ min: 1 }).withMessage('Count must be a positive integer'),
  query('period').isInt({ min: 1 }).withMessage('Period must be a positive integer'),
  query('version').optional().isIn(['4', '3', '6']).withMessage('Invalid version')
], async (req: AuthRequest, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const { count, period, version = '6' } = req.query;
    
    const apiKey = process.env.PX6_DEFAULT_API_KEY;
    if (!apiKey) {
      return res.status(500).json({
        error: 'API key not configured',
        message: 'PX6 API key is not configured'
      });
    }

    const px6Service = new PX6Service(apiKey);
    const result = await px6Service.getPrice(
      parseInt(count as string),
      parseInt(period as string),
      version as '4' | '3' | '6'
    );

    res.json({
      count: result.count,
      period: result.period,
      version,
      total_price: result.price,
      price_per_proxy: result.price_single,
      currency: result.currency
    });
  } catch (error) {
    console.error('Get price error:', error);
    res.status(500).json({
      error: 'Failed to get price',
      message: error instanceof Error ? error.message : 'Internal server error'
    });
  }
});

// 获取用户的代理列表
router.get('/my-proxies', authenticateToken, [
  query('page').optional().isInt({ min: 1 }).withMessage('Page must be a positive integer'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('status').optional().isIn(['active', 'expired', 'all']).withMessage('Invalid status'),
  query('country').optional().isLength({ min: 2, max: 2 }).withMessage('Invalid country code'),
  query('type').optional().isIn(['http', 'socks']).withMessage('Invalid proxy type')
], async (req: AuthRequest, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const page = parseInt(req.query.page as string) || 1;
    const limit = parseInt(req.query.limit as string) || 10;
    const filters = {
      status: req.query.status as string,
      country: req.query.country as string,
      proxy_type: req.query.type as string
    };

    // 获取用户的代理订单
    const { orders, total } = await ProxyOrder.findByUserId(req.user.id, page, limit, filters);

    // 获取每个订单的代理详情
    const ordersWithDetails = await Promise.all(
      orders.map(async (order) => {
        const details = await ProxyDetail.findByOrderId(order.id!);
        return {
          ...order,
          proxies: details
        };
      })
    );

    res.json({
      orders: ordersWithDetails,
      pagination: {
        current_page: page,
        per_page: limit,
        total: total,
        total_pages: Math.ceil(total / limit)
      }
    });

  } catch (error) {
    console.error('Get my proxies error:', error);
    res.status(500).json({
      error: 'Failed to get proxies',
      message: error instanceof Error ? error.message : 'Internal server error'
    });
  }
});

// 获取单个订单详情
router.get('/orders/:orderId', authenticateToken, async (req: AuthRequest, res) => {
  try {
    const orderId = parseInt(req.params.orderId);
    const order = await ProxyOrder.findById(orderId);

    if (!order) {
      return res.status(404).json({
        error: 'Order not found',
        message: 'The specified order does not exist'
      });
    }

    // 验证订单是否属于当前用户（非管理员）
    if (req.user.role !== 'admin' && order.user_id !== req.user.id) {
      return res.status(403).json({
        error: 'Access denied',
        message: 'You can only access your own orders'
      });
    }

    // 获取代理详情
    const proxies = await ProxyDetail.findByOrderId(orderId);

    res.json({
      order: {
        ...order,
        proxies: proxies
      }
    });

  } catch (error) {
    console.error('Get order details error:', error);
    res.status(500).json({
      error: 'Failed to get order details',
      message: error instanceof Error ? error.message : 'Internal server error'
    });
  }
});

// 购买代理
router.post('/buy', authenticateToken, [
  body('count').isInt({ min: 1, max: 1000 }).withMessage('Count must be between 1 and 1000'),
  body('period').isInt({ min: 1, max: 365 }).withMessage('Period must be between 1 and 365 days'),
  body('country').isLength({ min: 2, max: 2 }).withMessage('Country must be a 2-letter code'),
  body('version').optional().isIn(['4', '3', '6']).withMessage('Invalid version'),
  body('type').optional().isIn(['http', 'socks']).withMessage('Invalid proxy type'),
  body('api_key_id').isInt({ min: 1 }).withMessage('API key ID is required'),
  body('description').optional().isLength({ max: 50 }).withMessage('Description too long')
], async (req: AuthRequest, res) => {
  try {
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      return res.status(400).json({
        error: 'Validation failed',
        details: errors.array()
      });
    }

    const {
      count,
      period,
      country,
      version = '6',
      type = 'http',
      api_key_id,
      description = '',
      auto_prolong = false
    } = req.body;

    // 验证API密钥是否属于当前用户
    const apiKeyOwnership = await ApiKey.verifyOwnership(api_key_id, req.user.id);
    if (!apiKeyOwnership) {
      return res.status(403).json({
        error: 'Invalid API key',
        message: 'The specified API key does not belong to you'
      });
    }

    // 获取API密钥
    const apiKey = await ApiKey.findById(api_key_id);
    if (!apiKey || !apiKey.is_active) {
      return res.status(400).json({
        error: 'API key not available',
        message: 'The specified API key is not active'
      });
    }

    // 获取价格信息
    const px6Service = new PX6Service(apiKey.px6_api_key);
    const priceInfo = await px6Service.getPrice(count, period, version as '4' | '3' | '6');

    // 检查用户余额
    if (req.user.balance < priceInfo.price!) {
      return res.status(400).json({
        error: 'Insufficient balance',
        message: `You need ${priceInfo.currency} ${priceInfo.price} but only have ${req.user.currency} ${req.user.balance}`
      });
    }

    // 使用事务处理购买流程
    const result = await database.transaction(async (connection) => {
      // 扣除用户余额
      await User.updateBalance(req.user.id, priceInfo.price!, 'subtract');

      // 创建订单记录
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + period);

      const order = await ProxyOrder.create({
        user_id: req.user.id,
        api_key_id: api_key_id,
        country: country,
        proxy_count: count,
        period_days: period,
        proxy_version: version as '4' | '3' | '6',
        proxy_type: type as 'http' | 'socks',
        total_price: priceInfo.price!,
        currency: priceInfo.currency as 'USD' | 'RUB',
        status: 'pending',
        description: description,
        auto_prolong: auto_prolong,
        expires_at: expiresAt
      });

      // 调用PX6 API购买代理
      const buyResult = await px6Service.buyProxies({
        count,
        period,
        country,
        version: version as '4' | '3' | '6',
        type: type as 'http' | 'socks',
        descr: description,
        auto_prolong: auto_prolong
      });

      // 更新订单状态
      await ProxyOrder.updateStatus(order.id!, 'active');

      // 保存代理详情
      if (buyResult.list) {
        const proxyDetails = Object.values(buyResult.list).map(proxy => ({
          order_id: order.id!,
          px6_proxy_id: proxy.id,
          ip_address: proxy.ip,
          host: proxy.host,
          port: parseInt(proxy.port),
          username: proxy.user,
          password: proxy.pass,
          proxy_type: proxy.type as 'http' | 'socks',
          country: proxy.country,
          is_active: proxy.active === '1',
          date_start: new Date(proxy.date),
          date_end: new Date(proxy.date_end),
          technical_comment: proxy.descr
        }));

        await ProxyDetail.createBatch(proxyDetails);
      }

      return { order, buyResult };
    });

    res.json({
      message: 'Proxies purchased successfully',
      order: result.order,
      proxies: result.buyResult.list ? Object.values(result.buyResult.list) : [],
      total_cost: priceInfo.price,
      currency: priceInfo.currency
    });

  } catch (error) {
    console.error('Buy proxies error:', error);
    res.status(500).json({
      error: 'Failed to buy proxies',
      message: error instanceof Error ? error.message : 'Internal server error'
    });
  }
});

export default router;
