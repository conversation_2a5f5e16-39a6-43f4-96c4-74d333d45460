import axios, { AxiosInstance } from 'axios';

export interface PX6Response {
  status: 'yes' | 'no';
  user_id?: string;
  balance?: string;
  currency?: string;
  error_id?: number;
  error?: string;
}

export interface PX6PriceResponse extends PX6Response {
  price?: number;
  price_single?: number;
  period?: number;
  count?: number;
}

export interface PX6CountResponse extends PX6Response {
  count?: number;
}

export interface PX6CountryResponse extends PX6Response {
  list?: string[];
}

export interface PX6ProxyInfo {
  id: string;
  ip: string;
  host: string;
  port: string;
  user: string;
  pass: string;
  type: 'http' | 'socks';
  country: string;
  date: string;
  date_end: string;
  unixtime: number;
  unixtime_end: number;
  descr: string;
  active: string;
}

export interface PX6ProxyResponse extends PX6Response {
  list_count?: number;
  list?: { [key: string]: PX6ProxyInfo };
}

export interface PX6BuyResponse extends PX6Response {
  count?: number;
  price?: number;
  price_single?: number;
  period?: number;
  country?: string;
  list?: { [key: string]: PX6ProxyInfo };
}

export interface PX6CheckResponse extends PX6Response {
  proxy_id?: number;
  proxy_status?: boolean;
}

export class PX6Service {
  private apiClient: AxiosInstance;
  private baseUrl: string;
  private apiKey: string;

  constructor(apiKey: string) {
    this.apiKey = apiKey;
    this.baseUrl = process.env.PX6_API_BASE_URL || 'https://px6.link/api';
    
    this.apiClient = axios.create({
      baseURL: this.baseUrl,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'Proxy-System/1.0'
      }
    });

    // 请求拦截器 - 添加速率限制
    this.apiClient.interceptors.request.use(async (config) => {
      // 简单的速率限制实现
      await this.rateLimitDelay();
      return config;
    });
  }

  // 速率限制延迟
  private async rateLimitDelay(): Promise<void> {
    const delay = 350; // 每秒最多3次请求，所以间隔350ms
    return new Promise(resolve => setTimeout(resolve, delay));
  }

  // 构建API URL
  private buildUrl(method: string, params?: Record<string, any>): string {
    let url = `${this.baseUrl}/${this.apiKey}`;
    
    if (method) {
      url += `/${method}`;
    }

    if (params) {
      const searchParams = new URLSearchParams();
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          searchParams.append(key, value.toString());
        }
      });
      
      if (searchParams.toString()) {
        url += `?${searchParams.toString()}`;
      }
    }

    return url;
  }

  // 发送GET请求
  private async makeRequest<T extends PX6Response>(method: string, params?: Record<string, any>): Promise<T> {
    try {
      const url = this.buildUrl(method, params);
      const response = await this.apiClient.get(url);
      
      if (response.data.status === 'no') {
        throw new Error(`PX6 API Error ${response.data.error_id}: ${response.data.error}`);
      }
      
      return response.data;
    } catch (error) {
      if (axios.isAxiosError(error)) {
        if (error.response?.status === 429) {
          throw new Error('API rate limit exceeded. Please try again later.');
        }
        throw new Error(`API request failed: ${error.message}`);
      }
      throw error;
    }
  }

  // 获取价格信息
  async getPrice(count: number, period: number, version: '4' | '3' | '6' = '6'): Promise<PX6PriceResponse> {
    return this.makeRequest<PX6PriceResponse>('getprice', { count, period, version });
  }

  // 获取可用代理数量
  async getCount(country: string, version: '4' | '3' | '6' = '6'): Promise<PX6CountResponse> {
    return this.makeRequest<PX6CountResponse>('getcount', { country, version });
  }

  // 获取可用国家列表
  async getCountries(version: '4' | '3' | '6' = '6'): Promise<PX6CountryResponse> {
    return this.makeRequest<PX6CountryResponse>('getcountry', { version });
  }

  // 获取代理列表
  async getProxies(options?: {
    state?: 'active' | 'expired' | 'expiring' | 'all';
    descr?: string;
    nokey?: boolean;
    page?: number;
    limit?: number;
  }): Promise<PX6ProxyResponse> {
    const params: Record<string, any> = {};
    
    if (options?.state) params.state = options.state;
    if (options?.descr) params.descr = options.descr;
    if (options?.nokey) params.nokey = '';
    if (options?.page) params.page = options.page;
    if (options?.limit) params.limit = options.limit;

    return this.makeRequest<PX6ProxyResponse>('getproxy', params);
  }

  // 购买代理
  async buyProxies(options: {
    count: number;
    period: number;
    country: string;
    version?: '4' | '3' | '6';
    type?: 'http' | 'socks';
    descr?: string;
    auto_prolong?: boolean;
    nokey?: boolean;
  }): Promise<PX6BuyResponse> {
    const params: Record<string, any> = {
      count: options.count,
      period: options.period,
      country: options.country
    };

    if (options.version) params.version = options.version;
    if (options.type) params.type = options.type;
    if (options.descr) params.descr = options.descr;
    if (options.auto_prolong) params.auto_prolong = '';
    if (options.nokey) params.nokey = '';

    return this.makeRequest<PX6BuyResponse>('buy', params);
  }

  // 延长代理
  async prolongProxies(ids: string[], period: number, nokey?: boolean): Promise<any> {
    const params: Record<string, any> = {
      period,
      ids: ids.join(',')
    };

    if (nokey) params.nokey = '';

    return this.makeRequest('prolong', params);
  }

  // 设置代理类型
  async setProxyType(ids: string[], type: 'http' | 'socks'): Promise<PX6Response> {
    return this.makeRequest('settype', {
      ids: ids.join(','),
      type
    });
  }

  // 设置代理描述
  async setProxyDescription(options: {
    new: string;
    old?: string;
    ids?: string[];
  }): Promise<any> {
    const params: Record<string, any> = {
      new: options.new
    };

    if (options.old) params.old = options.old;
    if (options.ids) params.ids = options.ids.join(',');

    return this.makeRequest('setdescr', params);
  }

  // 删除代理
  async deleteProxies(options: {
    ids?: string[];
    descr?: string;
  }): Promise<any> {
    const params: Record<string, any> = {};

    if (options.ids) params.ids = options.ids.join(',');
    if (options.descr) params.descr = options.descr;

    return this.makeRequest('delete', params);
  }

  // 检查代理状态
  async checkProxy(id: string): Promise<PX6CheckResponse> {
    return this.makeRequest<PX6CheckResponse>('check', { ids: id });
  }

  // 测试API连接
  async testConnection(): Promise<PX6Response> {
    return this.makeRequest<PX6Response>('');
  }
}
