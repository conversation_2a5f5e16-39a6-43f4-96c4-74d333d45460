import database from '../config/database';

export interface ITransaction {
  id?: number;
  user_id: number;
  type: 'deposit' | 'withdraw' | 'purchase' | 'refund';
  amount: number;
  currency: 'USD' | 'RUB';
  description?: string;
  reference_id?: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  created_at?: Date;
  updated_at?: Date;
}

export class Transaction {
  // 创建交易记录
  static async create(transactionData: Omit<ITransaction, 'id' | 'created_at' | 'updated_at'>): Promise<ITransaction> {
    const {
      user_id,
      type,
      amount,
      currency,
      description,
      reference_id,
      status = 'pending'
    } = transactionData;

    const sql = `
      INSERT INTO transactions (user_id, type, amount, currency, description, reference_id, status)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await database.query(sql, [
      user_id, type, amount, currency, description, reference_id, status
    ]);

    return this.findById(result.insertId);
  }

  // 根据ID查找交易记录
  static async findById(id: number): Promise<ITransaction | null> {
    const sql = 'SELECT * FROM transactions WHERE id = ?';
    const transactions = await database.query(sql, [id]);
    return transactions.length > 0 ? transactions[0] : null;
  }

  // 根据用户ID获取交易记录
  static async findByUserId(
    userId: number,
    page: number = 1,
    limit: number = 10,
    filters?: any
  ): Promise<{ transactions: ITransaction[], total: number }> {
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE user_id = ?';
    let params: any[] = [userId];

    if (filters) {
      if (filters.type) {
        whereClause += ' AND type = ?';
        params.push(filters.type);
      }
      if (filters.status) {
        whereClause += ' AND status = ?';
        params.push(filters.status);
      }
      if (filters.start_date) {
        whereClause += ' AND created_at >= ?';
        params.push(filters.start_date);
      }
      if (filters.end_date) {
        whereClause += ' AND created_at <= ?';
        params.push(filters.end_date);
      }
    }

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM transactions ${whereClause}`;
    const countResult = await database.query(countSql, params);
    const total = countResult[0].total;

    // 获取交易记录列表
    const sql = `
      SELECT * FROM transactions ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const transactions = await database.query(sql, [...params, limit, offset]);
    return { transactions, total };
  }

  // 更新交易状态
  static async updateStatus(id: number, status: string): Promise<boolean> {
    const sql = 'UPDATE transactions SET status = ? WHERE id = ?';
    const result = await database.query(sql, [status, id]);
    return result.affectedRows > 0;
  }

  // 获取用户的交易统计
  static async getUserStats(userId: number, period?: string): Promise<any> {
    let dateCondition = '';
    const params = [userId];

    if (period) {
      switch (period) {
        case 'today':
          dateCondition = 'AND DATE(created_at) = CURDATE()';
          break;
        case 'week':
          dateCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
          break;
        case 'month':
          dateCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
          break;
        case 'year':
          dateCondition = 'AND created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)';
          break;
      }
    }

    const sql = `
      SELECT 
        type,
        currency,
        SUM(amount) as total_amount,
        COUNT(*) as count
      FROM transactions 
      WHERE user_id = ? AND status = 'completed' ${dateCondition}
      GROUP BY type, currency
    `;

    return await database.query(sql, params);
  }

  // 创建代理购买交易记录
  static async createPurchaseTransaction(
    userId: number,
    amount: number,
    currency: string,
    orderId: number,
    description?: string
  ): Promise<ITransaction> {
    return this.create({
      user_id: userId,
      type: 'purchase',
      amount: amount,
      currency: currency as 'USD' | 'RUB',
      description: description || 'Proxy purchase',
      reference_id: `order_${orderId}`,
      status: 'completed'
    });
  }

  // 创建充值交易记录
  static async createDepositTransaction(
    userId: number,
    amount: number,
    currency: string,
    referenceId?: string,
    description?: string
  ): Promise<ITransaction> {
    return this.create({
      user_id: userId,
      type: 'deposit',
      amount: amount,
      currency: currency as 'USD' | 'RUB',
      description: description || 'Account deposit',
      reference_id: referenceId,
      status: 'pending'
    });
  }

  // 创建退款交易记录
  static async createRefundTransaction(
    userId: number,
    amount: number,
    currency: string,
    referenceId?: string,
    description?: string
  ): Promise<ITransaction> {
    return this.create({
      user_id: userId,
      type: 'refund',
      amount: amount,
      currency: currency as 'USD' | 'RUB',
      description: description || 'Refund',
      reference_id: referenceId,
      status: 'completed'
    });
  }

  // 获取所有交易记录（管理员）
  static async getAll(
    page: number = 1,
    limit: number = 10,
    filters?: any
  ): Promise<{ transactions: any[], total: number }> {
    const offset = (page - 1) * limit;
    let whereClause = '';
    let params: any[] = [];

    if (filters) {
      const conditions = [];
      if (filters.user_id) {
        conditions.push('t.user_id = ?');
        params.push(filters.user_id);
      }
      if (filters.type) {
        conditions.push('t.type = ?');
        params.push(filters.type);
      }
      if (filters.status) {
        conditions.push('t.status = ?');
        params.push(filters.status);
      }
      if (filters.start_date) {
        conditions.push('t.created_at >= ?');
        params.push(filters.start_date);
      }
      if (filters.end_date) {
        conditions.push('t.created_at <= ?');
        params.push(filters.end_date);
      }

      if (conditions.length > 0) {
        whereClause = 'WHERE ' + conditions.join(' AND ');
      }
    }

    // 获取总数
    const countSql = `
      SELECT COUNT(*) as total 
      FROM transactions t 
      ${whereClause}
    `;
    const countResult = await database.query(countSql, params);
    const total = countResult[0].total;

    // 获取交易记录列表（包含用户信息）
    const sql = `
      SELECT 
        t.*,
        u.username,
        u.email
      FROM transactions t
      LEFT JOIN users u ON t.user_id = u.id
      ${whereClause}
      ORDER BY t.created_at DESC
      LIMIT ? OFFSET ?
    `;

    const transactions = await database.query(sql, [...params, limit, offset]);
    return { transactions, total };
  }

  // 获取系统交易统计
  static async getSystemStats(period?: string): Promise<any> {
    let dateCondition = '';
    const params: any[] = [];

    if (period) {
      switch (period) {
        case 'today':
          dateCondition = 'WHERE DATE(created_at) = CURDATE()';
          break;
        case 'week':
          dateCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)';
          break;
        case 'month':
          dateCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)';
          break;
        case 'year':
          dateCondition = 'WHERE created_at >= DATE_SUB(NOW(), INTERVAL 365 DAY)';
          break;
      }
    }

    const sql = `
      SELECT 
        type,
        currency,
        status,
        SUM(amount) as total_amount,
        COUNT(*) as count
      FROM transactions 
      ${dateCondition}
      GROUP BY type, currency, status
    `;

    return await database.query(sql, params);
  }
}
