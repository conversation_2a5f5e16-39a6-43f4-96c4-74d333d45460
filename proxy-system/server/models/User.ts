import database from '../config/database';
import bcrypt from 'bcryptjs';

export interface IUser {
  id?: number;
  username: string;
  email: string;
  password_hash?: string;
  role: 'admin' | 'user';
  balance: number;
  currency: 'USD' | 'RUB';
  status: 'active' | 'inactive' | 'suspended';
  created_at?: Date;
  updated_at?: Date;
  last_login?: Date;
}

export class User {
  // 创建用户
  static async create(userData: Omit<IUser, 'id' | 'created_at' | 'updated_at'>): Promise<IUser> {
    const { username, email, password_hash, role = 'user', balance = 0, currency = 'USD', status = 'active' } = userData;
    
    const sql = `
      INSERT INTO users (username, email, password_hash, role, balance, currency, status)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `;
    
    const result = await database.query(sql, [username, email, password_hash, role, balance, currency, status]);
    return this.findById(result.insertId);
  }

  // 根据ID查找用户
  static async findById(id: number): Promise<IUser | null> {
    const sql = 'SELECT * FROM users WHERE id = ?';
    const users = await database.query(sql, [id]);
    return users.length > 0 ? users[0] : null;
  }

  // 根据用户名查找用户
  static async findByUsername(username: string): Promise<IUser | null> {
    const sql = 'SELECT * FROM users WHERE username = ?';
    const users = await database.query(sql, [username]);
    return users.length > 0 ? users[0] : null;
  }

  // 根据邮箱查找用户
  static async findByEmail(email: string): Promise<IUser | null> {
    const sql = 'SELECT * FROM users WHERE email = ?';
    const users = await database.query(sql, [email]);
    return users.length > 0 ? users[0] : null;
  }

  // 验证密码
  static async validatePassword(plainPassword: string, hashedPassword: string): Promise<boolean> {
    return bcrypt.compare(plainPassword, hashedPassword);
  }

  // 哈希密码
  static async hashPassword(password: string): Promise<string> {
    const rounds = parseInt(process.env.BCRYPT_ROUNDS || '10');
    return bcrypt.hash(password, rounds);
  }

  // 更新用户信息
  static async update(id: number, updateData: Partial<IUser>): Promise<IUser | null> {
    const fields = Object.keys(updateData).filter(key => key !== 'id');
    const values = fields.map(field => updateData[field as keyof IUser]);
    
    if (fields.length === 0) {
      return this.findById(id);
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const sql = `UPDATE users SET ${setClause} WHERE id = ?`;
    
    await database.query(sql, [...values, id]);
    return this.findById(id);
  }

  // 更新余额
  static async updateBalance(id: number, amount: number, operation: 'add' | 'subtract' = 'add'): Promise<boolean> {
    const operator = operation === 'add' ? '+' : '-';
    const sql = `UPDATE users SET balance = balance ${operator} ? WHERE id = ?`;
    
    const result = await database.query(sql, [Math.abs(amount), id]);
    return result.affectedRows > 0;
  }

  // 更新最后登录时间
  static async updateLastLogin(id: number): Promise<void> {
    const sql = 'UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?';
    await database.query(sql, [id]);
  }

  // 获取用户列表（分页）
  static async getList(page: number = 1, limit: number = 10, filters?: any): Promise<{ users: IUser[], total: number }> {
    const offset = (page - 1) * limit;
    let whereClause = '';
    let params: any[] = [];

    if (filters) {
      const conditions = [];
      if (filters.status) {
        conditions.push('status = ?');
        params.push(filters.status);
      }
      if (filters.role) {
        conditions.push('role = ?');
        params.push(filters.role);
      }
      if (filters.search) {
        conditions.push('(username LIKE ? OR email LIKE ?)');
        params.push(`%${filters.search}%`, `%${filters.search}%`);
      }
      
      if (conditions.length > 0) {
        whereClause = 'WHERE ' + conditions.join(' AND ');
      }
    }

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM users ${whereClause}`;
    const countResult = await database.query(countSql, params);
    const total = countResult[0].total;

    // 获取用户列表
    const sql = `
      SELECT id, username, email, role, balance, currency, status, created_at, updated_at, last_login
      FROM users ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    const users = await database.query(sql, [...params, limit, offset]);
    
    return { users, total };
  }

  // 删除用户
  static async delete(id: number): Promise<boolean> {
    const sql = 'DELETE FROM users WHERE id = ?';
    const result = await database.query(sql, [id]);
    return result.affectedRows > 0;
  }
}
