import database from '../config/database';

export interface IProxyOrder {
  id?: number;
  user_id: number;
  api_key_id: number;
  px6_order_id?: string;
  country: string;
  proxy_count: number;
  period_days: number;
  proxy_version: '4' | '3' | '6';
  proxy_type: 'http' | 'socks';
  total_price: number;
  currency: 'USD' | 'RUB';
  status: 'pending' | 'active' | 'expired' | 'cancelled';
  description?: string;
  auto_prolong: boolean;
  created_at?: Date;
  updated_at?: Date;
  expires_at?: Date;
}

export interface IProxyDetail {
  id?: number;
  order_id: number;
  px6_proxy_id: string;
  ip_address?: string;
  host?: string;
  port?: number;
  username?: string;
  password?: string;
  proxy_type: 'http' | 'socks';
  country?: string;
  is_active: boolean;
  date_start?: Date;
  date_end?: Date;
  technical_comment?: string;
  created_at?: Date;
  updated_at?: Date;
}

export class ProxyOrder {
  // 创建代理订单
  static async create(orderData: Omit<IProxyOrder, 'id' | 'created_at' | 'updated_at'>): Promise<IProxyOrder> {
    const {
      user_id,
      api_key_id,
      px6_order_id,
      country,
      proxy_count,
      period_days,
      proxy_version,
      proxy_type,
      total_price,
      currency,
      status = 'pending',
      description,
      auto_prolong = false,
      expires_at
    } = orderData;

    const sql = `
      INSERT INTO proxy_orders (
        user_id, api_key_id, px6_order_id, country, proxy_count, period_days,
        proxy_version, proxy_type, total_price, currency, status, description,
        auto_prolong, expires_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await database.query(sql, [
      user_id, api_key_id, px6_order_id, country, proxy_count, period_days,
      proxy_version, proxy_type, total_price, currency, status, description,
      auto_prolong, expires_at
    ]);

    return this.findById(result.insertId);
  }

  // 根据ID查找订单
  static async findById(id: number): Promise<IProxyOrder | null> {
    const sql = 'SELECT * FROM proxy_orders WHERE id = ?';
    const orders = await database.query(sql, [id]);
    return orders.length > 0 ? orders[0] : null;
  }

  // 根据用户ID获取订单列表
  static async findByUserId(
    userId: number,
    page: number = 1,
    limit: number = 10,
    filters?: any
  ): Promise<{ orders: IProxyOrder[], total: number }> {
    const offset = (page - 1) * limit;
    let whereClause = 'WHERE user_id = ?';
    let params: any[] = [userId];

    if (filters) {
      if (filters.status) {
        whereClause += ' AND status = ?';
        params.push(filters.status);
      }
      if (filters.country) {
        whereClause += ' AND country = ?';
        params.push(filters.country);
      }
      if (filters.proxy_type) {
        whereClause += ' AND proxy_type = ?';
        params.push(filters.proxy_type);
      }
    }

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM proxy_orders ${whereClause}`;
    const countResult = await database.query(countSql, params);
    const total = countResult[0].total;

    // 获取订单列表
    const sql = `
      SELECT * FROM proxy_orders ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;

    const orders = await database.query(sql, [...params, limit, offset]);
    return { orders, total };
  }

  // 更新订单状态
  static async updateStatus(id: number, status: string, px6OrderId?: string): Promise<boolean> {
    let sql = 'UPDATE proxy_orders SET status = ?';
    let params = [status];

    if (px6OrderId) {
      sql += ', px6_order_id = ?';
      params.push(px6OrderId);
    }

    sql += ' WHERE id = ?';
    params.push(id);

    const result = await database.query(sql, params);
    return result.affectedRows > 0;
  }

  // 更新订单
  static async update(id: number, updateData: Partial<IProxyOrder>): Promise<IProxyOrder | null> {
    const fields = Object.keys(updateData).filter(key => key !== 'id');
    const values = fields.map(field => updateData[field as keyof IProxyOrder]);

    if (fields.length === 0) {
      return this.findById(id);
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const sql = `UPDATE proxy_orders SET ${setClause} WHERE id = ?`;

    await database.query(sql, [...values, id]);
    return this.findById(id);
  }

  // 删除订单
  static async delete(id: number): Promise<boolean> {
    const sql = 'DELETE FROM proxy_orders WHERE id = ?';
    const result = await database.query(sql, [id]);
    return result.affectedRows > 0;
  }

  // 获取即将过期的订单
  static async getExpiringOrders(days: number = 3): Promise<IProxyOrder[]> {
    const sql = `
      SELECT * FROM proxy_orders 
      WHERE status = 'active' 
      AND expires_at <= DATE_ADD(NOW(), INTERVAL ? DAY)
      AND expires_at > NOW()
    `;
    return await database.query(sql, [days]);
  }
}

export class ProxyDetail {
  // 创建代理详情
  static async create(detailData: Omit<IProxyDetail, 'id' | 'created_at' | 'updated_at'>): Promise<IProxyDetail> {
    const {
      order_id,
      px6_proxy_id,
      ip_address,
      host,
      port,
      username,
      password,
      proxy_type,
      country,
      is_active = true,
      date_start,
      date_end,
      technical_comment
    } = detailData;

    const sql = `
      INSERT INTO proxy_details (
        order_id, px6_proxy_id, ip_address, host, port, username, password,
        proxy_type, country, is_active, date_start, date_end, technical_comment
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await database.query(sql, [
      order_id, px6_proxy_id, ip_address, host, port, username, password,
      proxy_type, country, is_active, date_start, date_end, technical_comment
    ]);

    return this.findById(result.insertId);
  }

  // 根据ID查找代理详情
  static async findById(id: number): Promise<IProxyDetail | null> {
    const sql = 'SELECT * FROM proxy_details WHERE id = ?';
    const details = await database.query(sql, [id]);
    return details.length > 0 ? details[0] : null;
  }

  // 根据订单ID获取代理详情列表
  static async findByOrderId(orderId: number): Promise<IProxyDetail[]> {
    const sql = 'SELECT * FROM proxy_details WHERE order_id = ? ORDER BY created_at ASC';
    return await database.query(sql, [orderId]);
  }

  // 根据用户ID获取所有代理详情
  static async findByUserId(userId: number, filters?: any): Promise<IProxyDetail[]> {
    let sql = `
      SELECT pd.* FROM proxy_details pd
      JOIN proxy_orders po ON pd.order_id = po.id
      WHERE po.user_id = ?
    `;
    let params = [userId];

    if (filters) {
      if (filters.is_active !== undefined) {
        sql += ' AND pd.is_active = ?';
        params.push(filters.is_active);
      }
      if (filters.country) {
        sql += ' AND pd.country = ?';
        params.push(filters.country);
      }
      if (filters.proxy_type) {
        sql += ' AND pd.proxy_type = ?';
        params.push(filters.proxy_type);
      }
    }

    sql += ' ORDER BY pd.created_at DESC';
    return await database.query(sql, params);
  }

  // 更新代理详情
  static async update(id: number, updateData: Partial<IProxyDetail>): Promise<IProxyDetail | null> {
    const fields = Object.keys(updateData).filter(key => key !== 'id');
    const values = fields.map(field => updateData[field as keyof IProxyDetail]);

    if (fields.length === 0) {
      return this.findById(id);
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const sql = `UPDATE proxy_details SET ${setClause} WHERE id = ?`;

    await database.query(sql, [...values, id]);
    return this.findById(id);
  }

  // 批量创建代理详情
  static async createBatch(detailsData: Omit<IProxyDetail, 'id' | 'created_at' | 'updated_at'>[]): Promise<boolean> {
    if (detailsData.length === 0) return true;

    const sql = `
      INSERT INTO proxy_details (
        order_id, px6_proxy_id, ip_address, host, port, username, password,
        proxy_type, country, is_active, date_start, date_end, technical_comment
      ) VALUES ?
    `;

    const values = detailsData.map(detail => [
      detail.order_id,
      detail.px6_proxy_id,
      detail.ip_address,
      detail.host,
      detail.port,
      detail.username,
      detail.password,
      detail.proxy_type,
      detail.country,
      detail.is_active,
      detail.date_start,
      detail.date_end,
      detail.technical_comment
    ]);

    const result = await database.query(sql, [values]);
    return result.affectedRows > 0;
  }
}
