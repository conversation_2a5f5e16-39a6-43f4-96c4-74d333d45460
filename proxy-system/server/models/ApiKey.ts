import database from '../config/database';

export interface IApiKey {
  id?: number;
  user_id: number;
  key_name: string;
  px6_api_key: string;
  is_active: boolean;
  created_at?: Date;
  updated_at?: Date;
}

export class ApiKey {
  // 创建API密钥
  static async create(keyData: Omit<IApiKey, 'id' | 'created_at' | 'updated_at'>): Promise<IApiKey> {
    const { user_id, key_name, px6_api_key, is_active = true } = keyData;

    const sql = `
      INSERT INTO api_keys (user_id, key_name, px6_api_key, is_active)
      VALUES (?, ?, ?, ?)
    `;

    const result = await database.query(sql, [user_id, key_name, px6_api_key, is_active]);
    return this.findById(result.insertId);
  }

  // 根据ID查找API密钥
  static async findById(id: number): Promise<IApiKey | null> {
    const sql = 'SELECT * FROM api_keys WHERE id = ?';
    const keys = await database.query(sql, [id]);
    return keys.length > 0 ? keys[0] : null;
  }

  // 根据用户ID获取API密钥列表
  static async findByUserId(userId: number): Promise<IApiKey[]> {
    const sql = 'SELECT * FROM api_keys WHERE user_id = ? ORDER BY created_at DESC';
    return await database.query(sql, [userId]);
  }

  // 根据用户ID获取活跃的API密钥
  static async findActiveByUserId(userId: number): Promise<IApiKey[]> {
    const sql = 'SELECT * FROM api_keys WHERE user_id = ? AND is_active = true ORDER BY created_at DESC';
    return await database.query(sql, [userId]);
  }

  // 获取用户的默认API密钥（第一个活跃的）
  static async getDefaultByUserId(userId: number): Promise<IApiKey | null> {
    const sql = 'SELECT * FROM api_keys WHERE user_id = ? AND is_active = true ORDER BY created_at ASC LIMIT 1';
    const keys = await database.query(sql, [userId]);
    return keys.length > 0 ? keys[0] : null;
  }

  // 更新API密钥
  static async update(id: number, updateData: Partial<IApiKey>): Promise<IApiKey | null> {
    const fields = Object.keys(updateData).filter(key => key !== 'id');
    const values = fields.map(field => updateData[field as keyof IApiKey]);

    if (fields.length === 0) {
      return this.findById(id);
    }

    const setClause = fields.map(field => `${field} = ?`).join(', ');
    const sql = `UPDATE api_keys SET ${setClause} WHERE id = ?`;

    await database.query(sql, [...values, id]);
    return this.findById(id);
  }

  // 切换API密钥状态
  static async toggleStatus(id: number): Promise<boolean> {
    const sql = 'UPDATE api_keys SET is_active = NOT is_active WHERE id = ?';
    const result = await database.query(sql, [id]);
    return result.affectedRows > 0;
  }

  // 删除API密钥
  static async delete(id: number): Promise<boolean> {
    const sql = 'DELETE FROM api_keys WHERE id = ?';
    const result = await database.query(sql, [id]);
    return result.affectedRows > 0;
  }

  // 验证API密钥是否属于用户
  static async verifyOwnership(id: number, userId: number): Promise<boolean> {
    const sql = 'SELECT COUNT(*) as count FROM api_keys WHERE id = ? AND user_id = ?';
    const result = await database.query(sql, [id, userId]);
    return result[0].count > 0;
  }
}
