{"name": "pstree.remy", "version": "1.1.8", "main": "lib/index.js", "prettier": {"trailingComma": "es5", "semi": true, "singleQuote": true}, "scripts": {"test": "tap tests/*.test.js", "_prepublish": "npm test"}, "keywords": ["ps", "pstree", "ps tree"], "author": "<PERSON><PERSON>", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/remy/pstree.git"}, "devDependencies": {"tap": "^11.0.0"}, "directories": {"test": "tests"}, "dependencies": {}, "description": "Collects the full tree of processes from /proc"}