"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = toInt;
var _assertString = _interopRequireDefault(require("./util/assertString"));
function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }
function toInt(str, radix) {
  (0, _assertString.default)(str);
  return parseInt(str, radix || 10);
}
module.exports = exports.default;
module.exports.default = exports.default;