{"$ref": "#/definitions/TsConfigSchema", "$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"ModuleTypes": {"type": "object"}, "TsConfigOptions": {"description": "Must be an interface to support `typescript-json-schema`.", "properties": {"compiler": {"default": "typescript", "description": "Specify a custom TypeScript compiler.", "type": "string"}, "compilerHost": {"default": false, "description": "Use TypeScript's compiler host API instead of the language service API.", "type": "boolean"}, "compilerOptions": {"additionalProperties": true, "allOf": [{"$ref": "https://schemastore.azurewebsites.net/schemas/json/tsconfig.json#definitions/compilerOptionsDefinition/properties/compilerOptions"}], "description": "JSON object to merge with TypeScript `compilerOptions`.", "properties": {}, "type": "object"}, "emit": {"default": false, "description": "Emit output files into `.ts-node` directory.", "type": "boolean"}, "esm": {"description": "Enable native ESM support.\n\nFor details, see https://typestrong.org/ts-node/docs/imports#native-ecmascript-modules", "type": "boolean"}, "experimentalReplAwait": {"description": "Allows the usage of top level await in REPL.\n\nUses node's implementation which accomplishes this with an AST syntax transformation.\n\nEnabled by default when tsconfig target is es2018 or above. Set to false to disable.\n\n**Note**: setting to `true` when tsconfig target is too low will throw an Error.  Leave as `undefined`\nto get default, automatic behavior.", "type": "boolean"}, "experimentalResolver": {"description": "Enable experimental features that re-map imports and require calls to support:\n`baseUrl`, `paths`, `rootDirs`, `.js` to `.ts` file extension mappings,\n`outDir` to `rootDir` mappings for composite projects and monorepos.\n\nFor details, see https://github.com/TypeStrong/ts-node/issues/1514", "type": "boolean"}, "experimentalSpecifierResolution": {"description": "Like node's `--experimental-specifier-resolution`, , but can also be set in your `tsconfig.json` for convenience.\n\nFor details, see https://nodejs.org/dist/latest-v18.x/docs/api/esm.html#customizing-esm-specifier-resolution-algorithm", "enum": ["explicit", "node"], "type": "string"}, "experimentalTsImportSpecifiers": {"description": "Allow using voluntary `.ts` file extension in import specifiers.\n\nTypically, in ESM projects, import specifiers must have an emit extension, `.js`, `.cjs`, or `.mjs`,\nand we automatically map to the corresponding `.ts`, `.cts`, or `.mts` source file.  This is the\nrecommended approach.\n\nHowever, if you really want to use `.ts` in import specifiers, and are aware that this may\nbreak tooling, you can enable this flag.", "type": "boolean"}, "files": {"default": false, "description": "Load \"files\" and \"include\" from `tsconfig.json` on startup.\n\nDefault is to override `tsconfig.json` \"files\" and \"include\" to only include the entrypoint script.", "type": "boolean"}, "ignore": {"default": ["(?:^|/)node_modules/"], "description": "Paths which should not be compiled.\n\nEach string in the array is converted to a regular expression via `new RegExp()` and tested against source paths prior to compilation.\n\nSource paths are normalized to posix-style separators, relative to the directory containing `tsconfig.json` or to cwd if no `tsconfig.json` is loaded.\n\nDefault is to ignore all node_modules subdirectories.", "items": {"type": "string"}, "type": "array"}, "ignoreDiagnostics": {"description": "Ignore TypeScript warnings by diagnostic code.", "items": {"type": ["string", "number"]}, "type": "array"}, "logError": {"default": false, "description": "Logs TypeScript errors to st<PERSON><PERSON> instead of throwing exceptions.", "type": "boolean"}, "moduleTypes": {"$ref": "#/definitions/ModuleTypes", "description": "Override certain paths to be compiled and executed as CommonJS or ECMAScript modules.\nWhen overridden, the tsconfig \"module\" and package.json \"type\" fields are overridden, and\nthe file extension is ignored.\nThis is useful if you cannot use .mts, .cts, .mjs, or .cjs file extensions;\nit achieves the same effect.\n\nEach key is a glob pattern following the same rules as tsconfig's \"include\" array.\nWhen multiple patterns match the same file, the last pattern takes precedence.\n\n`cjs` overrides matches files to compile and execute as CommonJS.\n`esm` overrides matches files to compile and execute as native ECMAScript modules.\n`package` overrides either of the above to default behavior, which obeys package.json \"type\" and\ntsconfig.json \"module\" options."}, "preferTsExts": {"default": false, "description": "Re-order file extensions so that TypeScript imports are preferred.\n\nFor example, when both `index.js` and `index.ts` exist, enabling this option causes `require('./index')` to resolve to `index.ts` instead of `index.js`", "type": "boolean"}, "pretty": {"default": false, "description": "Use pretty diagnostic formatter.", "type": "boolean"}, "require": {"description": "Modules to require, like node's `--require` flag.\n\nIf specified in `tsconfig.json`, the modules will be resolved relative to the `tsconfig.json` file.\n\nIf specified programmatically, each input string should be pre-resolved to an absolute path for\nbest results.", "items": {"type": "string"}, "type": "array"}, "scope": {"default": false, "description": "Scope compiler to files within `scopeDir`.", "type": "boolean"}, "scopeDir": {"default": "First of: `tsconfig.json` \"rootDir\" if specified, directory containing `tsconfig.json`, or cwd if no `tsconfig.json` is loaded.", "type": "string"}, "skipIgnore": {"default": false, "description": "<PERSON><PERSON> ignore check, so that compilation will be attempted for all files with matching extensions.", "type": "boolean"}, "swc": {"description": "Transpile with swc instead of the TypeScript compiler, and skip typechecking.\n\nEquivalent to setting both `transpileOnly: true` and `transpiler: 'ts-node/transpilers/swc'`\n\nFor complete instructions: https://typestrong.org/ts-node/docs/transpilers", "type": "boolean"}, "transpileOnly": {"default": false, "description": "Use TypeScript's faster `transpileModule`.", "type": "boolean"}, "transpiler": {"anyOf": [{"items": [{"type": "string"}, {"additionalProperties": true, "properties": {}, "type": "object"}], "maxItems": 2, "minItems": 2, "type": "array"}, {"type": "string"}], "description": "Specify a custom transpiler for use with transpileOnly"}, "typeCheck": {"default": true, "description": "**DEPRECATED** Specify type-check is enabled (e.g. `transpileOnly == false`).", "type": "boolean"}}, "type": "object"}, "TsConfigSchema": {"allOf": [{"$ref": "https://schemastore.azurewebsites.net/schemas/json/tsconfig.json"}], "description": "tsconfig schema which includes \"ts-node\" options.", "properties": {"ts-node": {"$ref": "#/definitions/TsConfigOptions", "description": "ts-node options.  See also: https://typestrong.org/ts-node/docs/configuration\n\nts-node offers TypeScript execution and REPL for node.js, with source map support."}}, "type": "object"}}}