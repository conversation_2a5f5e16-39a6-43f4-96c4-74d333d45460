const mysql = require('mysql2/promise');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.join(__dirname, '../server/.env') });

async function runMigration() {
  let connection;
  
  try {
    console.log('🚀 Starting database migration...');
    
    // 创建数据库连接
    connection = await mysql.createConnection({
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '3306'),
      user: process.env.DB_USER || 'root',
      password: process.env.DB_PASSWORD || '',
      multipleStatements: true
    });

    console.log('✅ Connected to MySQL server');

    // 读取SQL文件
    const sqlFile = path.join(__dirname, 'schema.sql');
    const sql = fs.readFileSync(sqlFile, 'utf8');

    console.log('📄 Executing SQL schema...');
    
    // 执行SQL
    await connection.execute(sql);
    
    console.log('✅ Database schema created successfully');

    // 创建默认管理员用户（如果不存在）
    const bcrypt = require('bcryptjs');
    const defaultPassword = 'admin123';
    const hashedPassword = await bcrypt.hash(defaultPassword, 10);

    const checkAdminSql = 'SELECT COUNT(*) as count FROM proxy_system.users WHERE username = ?';
    const [adminCheck] = await connection.execute(checkAdminSql, ['admin']);

    if (adminCheck[0].count === 0) {
      const createAdminSql = `
        INSERT INTO proxy_system.users (username, email, password_hash, role, balance, currency, status)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `;
      
      await connection.execute(createAdminSql, [
        'admin',
        '<EMAIL>',
        hashedPassword,
        'admin',
        0.00,
        'USD',
        'active'
      ]);
      
      console.log('✅ Default admin user created');
      console.log('   Username: admin');
      console.log('   Password: admin123');
      console.log('   ⚠️  Please change the default password after first login!');
    } else {
      console.log('ℹ️  Admin user already exists');
    }

    console.log('🎉 Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    process.exit(1);
  } finally {
    if (connection) {
      await connection.end();
    }
  }
}

// 检查是否直接运行此脚本
if (require.main === module) {
  runMigration();
}

module.exports = { runMigration };
