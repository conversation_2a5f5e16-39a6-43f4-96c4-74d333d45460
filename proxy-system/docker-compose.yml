version: '3.8'

services:
  # MariaDB 数据库
  database:
    image: mariadb:10.11
    container_name: proxy-system-db
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD:-root_password}
      MYSQL_DATABASE: ${DB_NAME:-proxy_system}
      MYSQL_USER: ${DB_USER:-proxy_user}
      MYSQL_PASSWORD: ${DB_PASSWORD:-proxy_password}
    volumes:
      - db_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
    ports:
      - "3306:3306"
    networks:
      - proxy-network
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      timeout: 20s
      retries: 10

  # Redis (可选，用于缓存和会话存储)
  redis:
    image: redis:7-alpine
    container_name: proxy-system-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - proxy-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      timeout: 3s
      retries: 5

  # 代理系统应用
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: proxy-system-app
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DB_HOST: database
      DB_PORT: 3306
      DB_USER: ${DB_USER:-proxy_user}
      DB_PASSWORD: ${DB_PASSWORD:-proxy_password}
      DB_NAME: ${DB_NAME:-proxy_system}
      JWT_SECRET: ${JWT_SECRET:-your_jwt_secret_change_this}
      PX6_API_BASE_URL: https://px6.link/api
      PX6_DEFAULT_API_KEY: ${PX6_API_KEY:-your_px6_api_key}
      CORS_ORIGIN: ${CORS_ORIGIN:-http://localhost}
    ports:
      - "3001:3001"
    volumes:
      - app_logs:/app/logs
    networks:
      - proxy-network
    depends_on:
      database:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/health"]
      timeout: 10s
      retries: 3
      start_period: 30s

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: proxy-system-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    networks:
      - proxy-network
    depends_on:
      - app

volumes:
  db_data:
    driver: local
  redis_data:
    driver: local
  app_logs:
    driver: local
  nginx_logs:
    driver: local

networks:
  proxy-network:
    driver: bridge
