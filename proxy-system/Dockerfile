# 多阶段构建 - 前端构建阶段
FROM node:18-alpine AS frontend-builder

WORKDIR /app

# 复制前端依赖文件
COPY package*.json ./
RUN npm ci --only=production

# 复制前端源码并构建
COPY . .
RUN npm run build

# 后端构建阶段
FROM node:18-alpine AS backend-builder

WORKDIR /app

# 复制后端依赖文件
COPY server/package*.json ./
RUN npm ci --only=production

# 复制后端源码并构建
COPY server/ .
RUN npm run build

# 生产环境阶段
FROM node:18-alpine AS production

# 安装必要的系统依赖
RUN apk add --no-cache \
    mysql-client \
    curl

# 创建应用用户
RUN addgroup -g 1001 -S nodejs
RUN adduser -S nextjs -u 1001

WORKDIR /app

# 复制后端构建结果
COPY --from=backend-builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=backend-builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=backend-builder --chown=nextjs:nodejs /app/package*.json ./

# 复制前端构建结果
COPY --from=frontend-builder --chown=nextjs:nodejs /app/dist ./public

# 复制数据库文件
COPY --chown=nextjs:nodejs database/ ./database/

# 创建日志目录
RUN mkdir -p /app/logs && chown nextjs:nodejs /app/logs

# 切换到应用用户
USER nextjs

# 暴露端口
EXPOSE 3001

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3001/health || exit 1

# 启动命令
CMD ["node", "dist/app.js"]
