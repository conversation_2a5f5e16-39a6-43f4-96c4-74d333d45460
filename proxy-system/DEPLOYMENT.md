# 代理系统部署指南

本文档详细说明如何部署代理管理系统到生产环境。

## 系统要求

### 最低配置
- CPU: 2核心
- 内存: 4GB RAM
- 存储: 20GB SSD
- 操作系统: Ubuntu 20.04+ / CentOS 8+ / Debian 11+

### 推荐配置
- CPU: 4核心
- 内存: 8GB RAM
- 存储: 50GB SSD
- 操作系统: Ubuntu 22.04 LTS

### 软件依赖
- Docker 20.10+
- Docker Compose 2.0+
- Node.js 18+ (开发环境)
- MySQL/MariaDB 10.6+ (如不使用Docker)

## 快速部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd proxy-system
```

### 2. 配置环境变量
```bash
# 复制生产环境配置
cp server/.env.production server/.env

# 编辑配置文件
nano server/.env
```

**重要配置项：**
- `JWT_SECRET`: 设置强密码（至少32字符）
- `DB_PASSWORD`: 设置数据库密码
- `PX6_DEFAULT_API_KEY`: 设置PX6.me API密钥
- `CORS_ORIGIN`: 设置前端域名

### 3. 运行部署脚本
```bash
# 生产环境部署
./scripts/deploy.sh production

# 开发环境部署
./scripts/deploy.sh development
```

## 手动部署

### 1. 环境准备

#### 安装Docker
```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 配置防火墙
```bash
# 开放必要端口
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 3001/tcp  # API端口（可选）
sudo ufw enable
```

### 2. 数据库设置

#### 使用Docker（推荐）
数据库将通过Docker Compose自动配置。

#### 手动安装MariaDB
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mariadb-server

# 安全配置
sudo mysql_secure_installation

# 创建数据库和用户
sudo mysql -u root -p
```

```sql
CREATE DATABASE proxy_system CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'proxy_user'@'localhost' IDENTIFIED BY 'your_secure_password';
GRANT ALL PRIVILEGES ON proxy_system.* TO 'proxy_user'@'localhost';
FLUSH PRIVILEGES;
EXIT;
```

### 3. SSL证书配置

#### 使用Let's Encrypt（推荐）
```bash
# 安装Certbot
sudo apt install certbot

# 获取证书
sudo certbot certonly --standalone -d your-domain.com

# 复制证书到项目目录
sudo mkdir -p nginx/ssl
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/certificate.crt
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/private.key
sudo chown -R $USER:$USER nginx/ssl
```

#### 自签名证书（测试用）
```bash
mkdir -p nginx/ssl
openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
    -keyout nginx/ssl/private.key \
    -out nginx/ssl/certificate.crt
```

### 4. 启动服务

```bash
# 构建并启动所有服务
docker-compose up --build -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 5. 验证部署

```bash
# 检查API健康状态
curl http://localhost:3001/health

# 检查前端访问
curl http://localhost

# 运行监控脚本
./scripts/monitor.sh
```

## 配置说明

### 环境变量详解

| 变量名 | 说明 | 示例值 |
|--------|------|--------|
| `NODE_ENV` | 运行环境 | `production` |
| `PORT` | API服务端口 | `3001` |
| `DB_HOST` | 数据库主机 | `localhost` |
| `DB_USER` | 数据库用户 | `proxy_user` |
| `DB_PASSWORD` | 数据库密码 | `secure_password` |
| `JWT_SECRET` | JWT密钥 | `your_32_char_secret` |
| `PX6_DEFAULT_API_KEY` | PX6 API密钥 | `your_px6_key` |
| `CORS_ORIGIN` | 允许的前端域名 | `https://your-domain.com` |

### Docker Compose配置

主要服务：
- `database`: MariaDB数据库
- `redis`: Redis缓存（可选）
- `app`: 主应用服务
- `nginx`: 反向代理

### Nginx配置

- 自动HTTP到HTTPS重定向
- API请求代理到后端
- 静态文件服务
- 速率限制和安全头
- SSL/TLS配置

## 维护操作

### 日常维护

```bash
# 查看服务状态
docker-compose ps

# 重启服务
docker-compose restart

# 更新应用
git pull
docker-compose up --build -d

# 备份数据库
docker-compose exec database mysqldump -u root -p proxy_system > backup.sql

# 恢复数据库
docker-compose exec -T database mysql -u root -p proxy_system < backup.sql
```

### 监控和日志

```bash
# 运行系统监控
./scripts/monitor.sh

# 查看应用日志
docker-compose logs app

# 查看数据库日志
docker-compose logs database

# 查看Nginx日志
docker-compose logs nginx
```

### 性能优化

1. **数据库优化**
   - 定期清理过期数据
   - 优化查询索引
   - 配置连接池

2. **缓存配置**
   - 启用Redis缓存
   - 配置静态文件缓存
   - 使用CDN加速

3. **负载均衡**
   - 多实例部署
   - 使用负载均衡器
   - 数据库读写分离

## 故障排除

### 常见问题

1. **服务无法启动**
   ```bash
   # 检查端口占用
   sudo netstat -tlnp | grep :3001
   
   # 检查Docker状态
   docker-compose ps
   docker-compose logs
   ```

2. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose exec database mysql -u root -p -e "SELECT 1;"
   
   # 重置数据库密码
   docker-compose exec database mysql -u root -p
   ```

3. **API请求失败**
   ```bash
   # 检查API健康状态
   curl -v http://localhost:3001/health
   
   # 检查防火墙设置
   sudo ufw status
   ```

### 日志位置

- 应用日志: `docker-compose logs app`
- 数据库日志: `docker-compose logs database`
- Nginx日志: `docker-compose logs nginx`
- 系统监控日志: `/var/log/proxy-system-monitor.log`

## 安全建议

1. **定期更新**
   - 及时更新系统补丁
   - 更新Docker镜像
   - 更新依赖包

2. **访问控制**
   - 使用强密码
   - 启用防火墙
   - 限制SSH访问

3. **数据保护**
   - 定期备份数据
   - 加密敏感数据
   - 监控异常访问

4. **SSL/TLS**
   - 使用有效SSL证书
   - 配置HSTS
   - 禁用弱加密算法

## 扩展部署

### 多服务器部署

1. **数据库分离**
   - 独立数据库服务器
   - 主从复制配置
   - 读写分离

2. **负载均衡**
   - 多个应用实例
   - Nginx负载均衡
   - 会话保持

3. **容器编排**
   - Kubernetes部署
   - Docker Swarm
   - 自动扩缩容

### 监控和告警

1. **系统监控**
   - Prometheus + Grafana
   - 自定义监控脚本
   - 健康检查

2. **日志聚合**
   - ELK Stack
   - 集中日志管理
   - 日志分析

3. **告警通知**
   - 邮件告警
   - 短信通知
   - Webhook集成

## 支持

如遇到部署问题，请：
1. 查看本文档的故障排除部分
2. 检查系统日志和错误信息
3. 提交Issue并附上详细的错误信息
