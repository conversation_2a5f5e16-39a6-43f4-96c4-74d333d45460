#!/bin/bash

# 代理系统部署脚本
# 使用方法: ./scripts/deploy.sh [environment]
# 环境: development, production

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查参数
ENVIRONMENT=${1:-development}

if [[ "$ENVIRONMENT" != "development" && "$ENVIRONMENT" != "production" ]]; then
    log_error "Invalid environment. Use 'development' or 'production'"
    exit 1
fi

log_info "Starting deployment for $ENVIRONMENT environment..."

# 检查必要的工具
check_dependencies() {
    log_info "Checking dependencies..."
    
    if ! command -v node &> /dev/null; then
        log_error "Node.js is not installed"
        exit 1
    fi
    
    if ! command -v npm &> /dev/null; then
        log_error "npm is not installed"
        exit 1
    fi
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        if ! command -v docker &> /dev/null; then
            log_error "Docker is not installed"
            exit 1
        fi
        
        if ! command -v docker-compose &> /dev/null; then
            log_error "Docker Compose is not installed"
            exit 1
        fi
    fi
    
    log_success "All dependencies are available"
}

# 环境配置检查
check_environment() {
    log_info "Checking environment configuration..."
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        if [[ ! -f "server/.env.production" ]]; then
            log_error "Production environment file not found: server/.env.production"
            log_info "Please copy server/.env.production.example and configure it"
            exit 1
        fi
        
        # 检查关键配置
        source server/.env.production
        
        if [[ "$JWT_SECRET" == "your_very_secure_jwt_secret_key_at_least_32_characters_long" ]]; then
            log_error "Please change the default JWT_SECRET in server/.env.production"
            exit 1
        fi
        
        if [[ "$DB_PASSWORD" == "your_secure_database_password" ]]; then
            log_error "Please change the default DB_PASSWORD in server/.env.production"
            exit 1
        fi
    fi
    
    log_success "Environment configuration is valid"
}

# 构建前端
build_frontend() {
    log_info "Building frontend..."
    
    npm install
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        npm run build
    fi
    
    log_success "Frontend build completed"
}

# 构建后端
build_backend() {
    log_info "Building backend..."
    
    cd server
    npm install
    
    if [[ "$ENVIRONMENT" == "production" ]]; then
        npm run build
    fi
    
    cd ..
    log_success "Backend build completed"
}

# 数据库迁移
run_migration() {
    log_info "Running database migration..."
    
    if [[ "$ENVIRONMENT" == "development" ]]; then
        cd database
        node migrate.js
        cd ..
    else
        log_info "Database migration will be handled by Docker initialization"
    fi
    
    log_success "Database migration completed"
}

# 开发环境部署
deploy_development() {
    log_info "Deploying to development environment..."
    
    # 启动后端服务
    log_info "Starting backend server..."
    cd server
    npm run dev &
    BACKEND_PID=$!
    cd ..
    
    # 启动前端服务
    log_info "Starting frontend server..."
    npm run dev &
    FRONTEND_PID=$!
    
    log_success "Development servers started"
    log_info "Backend: http://localhost:3001"
    log_info "Frontend: http://localhost:5173"
    log_info "Press Ctrl+C to stop servers"
    
    # 等待中断信号
    trap "kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; exit" INT TERM
    wait
}

# 生产环境部署
deploy_production() {
    log_info "Deploying to production environment..."
    
    # 停止现有容器
    log_info "Stopping existing containers..."
    docker-compose down || true
    
    # 构建并启动容器
    log_info "Building and starting containers..."
    docker-compose up --build -d
    
    # 等待服务启动
    log_info "Waiting for services to start..."
    sleep 30
    
    # 检查服务状态
    if docker-compose ps | grep -q "Up"; then
        log_success "Production deployment completed successfully"
        log_info "Application is running at: http://localhost"
        log_info "API is available at: http://localhost/api"
        
        # 显示容器状态
        log_info "Container status:"
        docker-compose ps
    else
        log_error "Deployment failed. Check container logs:"
        docker-compose logs
        exit 1
    fi
}

# 清理函数
cleanup() {
    log_info "Cleaning up..."
    
    if [[ "$ENVIRONMENT" == "development" ]]; then
        # 清理开发环境
        pkill -f "npm run dev" || true
    fi
}

# 主函数
main() {
    # 设置清理陷阱
    trap cleanup EXIT
    
    # 执行部署步骤
    check_dependencies
    check_environment
    build_frontend
    build_backend
    
    if [[ "$ENVIRONMENT" == "development" ]]; then
        run_migration
        deploy_development
    else
        deploy_production
    fi
}

# 运行主函数
main
