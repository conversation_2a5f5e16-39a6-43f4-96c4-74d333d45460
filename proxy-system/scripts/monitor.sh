#!/bin/bash

# 系统监控脚本
# 监控代理系统的运行状态

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
HEALTH_URL="http://localhost:3001/health"
LOG_FILE="/var/log/proxy-system-monitor.log"
ALERT_EMAIL=""  # 设置告警邮箱

# 日志函数
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# 检查服务健康状态
check_health() {
    local response
    local status_code
    
    response=$(curl -s -w "%{http_code}" "$HEALTH_URL" 2>/dev/null || echo "000")
    status_code="${response: -3}"
    
    if [[ "$status_code" == "200" ]]; then
        echo -e "${GREEN}✓${NC} API服务正常"
        return 0
    else
        echo -e "${RED}✗${NC} API服务异常 (HTTP $status_code)"
        log_message "ERROR: API health check failed with status $status_code"
        return 1
    fi
}

# 检查数据库连接
check_database() {
    if command -v mysql &> /dev/null; then
        if mysql -h localhost -u root -p"${DB_PASSWORD:-}" -e "SELECT 1;" &>/dev/null; then
            echo -e "${GREEN}✓${NC} 数据库连接正常"
            return 0
        else
            echo -e "${RED}✗${NC} 数据库连接失败"
            log_message "ERROR: Database connection failed"
            return 1
        fi
    else
        echo -e "${YELLOW}!${NC} MySQL客户端未安装，跳过数据库检查"
        return 0
    fi
}

# 检查Docker容器状态
check_docker() {
    if command -v docker-compose &> /dev/null; then
        local containers_status
        containers_status=$(docker-compose ps --services --filter "status=running" 2>/dev/null | wc -l)
        local total_containers
        total_containers=$(docker-compose ps --services 2>/dev/null | wc -l)
        
        if [[ "$containers_status" -eq "$total_containers" ]] && [[ "$total_containers" -gt 0 ]]; then
            echo -e "${GREEN}✓${NC} Docker容器运行正常 ($containers_status/$total_containers)"
            return 0
        else
            echo -e "${RED}✗${NC} Docker容器状态异常 ($containers_status/$total_containers)"
            log_message "ERROR: Docker containers not running properly"
            return 1
        fi
    else
        echo -e "${YELLOW}!${NC} Docker Compose未安装，跳过容器检查"
        return 0
    fi
}

# 检查磁盘空间
check_disk_space() {
    local usage
    usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ "$usage" -lt 80 ]]; then
        echo -e "${GREEN}✓${NC} 磁盘空间充足 (${usage}%)"
        return 0
    elif [[ "$usage" -lt 90 ]]; then
        echo -e "${YELLOW}!${NC} 磁盘空间警告 (${usage}%)"
        log_message "WARNING: Disk space usage is ${usage}%"
        return 1
    else
        echo -e "${RED}✗${NC} 磁盘空间不足 (${usage}%)"
        log_message "ERROR: Disk space critically low at ${usage}%"
        return 1
    fi
}

# 检查内存使用
check_memory() {
    local usage
    usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
    
    if [[ "$usage" -lt 80 ]]; then
        echo -e "${GREEN}✓${NC} 内存使用正常 (${usage}%)"
        return 0
    elif [[ "$usage" -lt 90 ]]; then
        echo -e "${YELLOW}!${NC} 内存使用警告 (${usage}%)"
        log_message "WARNING: Memory usage is ${usage}%"
        return 1
    else
        echo -e "${RED}✗${NC} 内存使用过高 (${usage}%)"
        log_message "ERROR: Memory usage critically high at ${usage}%"
        return 1
    fi
}

# 检查进程
check_processes() {
    local node_processes
    node_processes=$(pgrep -f "node.*app.js" | wc -l)
    
    if [[ "$node_processes" -gt 0 ]]; then
        echo -e "${GREEN}✓${NC} Node.js进程运行正常 ($node_processes 个进程)"
        return 0
    else
        echo -e "${RED}✗${NC} 未找到Node.js进程"
        log_message "ERROR: No Node.js processes found"
        return 1
    fi
}

# 发送告警
send_alert() {
    local message="$1"
    
    if [[ -n "$ALERT_EMAIL" ]]; then
        echo "$message" | mail -s "代理系统告警" "$ALERT_EMAIL" 2>/dev/null || true
    fi
    
    log_message "ALERT: $message"
}

# 主监控函数
run_monitor() {
    echo "=== 代理系统监控报告 $(date) ==="
    echo
    
    local errors=0
    local warnings=0
    
    # 执行各项检查
    check_health || ((errors++))
    check_database || ((errors++))
    check_docker || ((errors++))
    check_disk_space || ((warnings++))
    check_memory || ((warnings++))
    check_processes || ((errors++))
    
    echo
    echo "=== 监控总结 ==="
    
    if [[ "$errors" -eq 0 ]] && [[ "$warnings" -eq 0 ]]; then
        echo -e "${GREEN}✓ 系统运行正常${NC}"
        log_message "INFO: System monitoring passed - no issues detected"
    elif [[ "$errors" -eq 0 ]]; then
        echo -e "${YELLOW}! 系统运行正常，但有 $warnings 个警告${NC}"
        log_message "WARNING: System monitoring completed with $warnings warnings"
    else
        echo -e "${RED}✗ 检测到 $errors 个错误和 $warnings 个警告${NC}"
        send_alert "代理系统检测到 $errors 个错误和 $warnings 个警告，请检查系统状态"
        log_message "ERROR: System monitoring failed with $errors errors and $warnings warnings"
        exit 1
    fi
}

# 显示日志
show_logs() {
    echo "=== 最近的系统日志 ==="
    if [[ -f "$LOG_FILE" ]]; then
        tail -20 "$LOG_FILE"
    else
        echo "日志文件不存在: $LOG_FILE"
    fi
}

# 清理日志
cleanup_logs() {
    if [[ -f "$LOG_FILE" ]]; then
        # 保留最近1000行日志
        tail -1000 "$LOG_FILE" > "${LOG_FILE}.tmp"
        mv "${LOG_FILE}.tmp" "$LOG_FILE"
        echo "日志文件已清理"
    fi
}

# 主函数
main() {
    case "${1:-monitor}" in
        "monitor")
            run_monitor
            ;;
        "logs")
            show_logs
            ;;
        "cleanup")
            cleanup_logs
            ;;
        *)
            echo "使用方法: $0 [monitor|logs|cleanup]"
            echo "  monitor  - 运行系统监控检查"
            echo "  logs     - 显示最近的监控日志"
            echo "  cleanup  - 清理旧的日志文件"
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
