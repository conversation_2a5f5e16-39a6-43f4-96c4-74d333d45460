module.exports =
  typeof EdgeRuntime === 'string' ? edge() : require("next/dist/compiled/@edge-runtime/primitives")

function edge() {
  return {
    AbortController,
    AbortSignal,
    atob,
    Blob,
    btoa,
    console,
    crypto,
    Crypto,
    CryptoKey,
    DOMException,
    Event,
    EventTarget,
    fetch,
    FetchEvent,
    File,
    FormData,
    Headers,
    performance,
    PromiseRejectionEvent,
    ReadableStream,
    ReadableStreamBYOBReader,
    ReadableStreamDefaultReader,
    Request,
    Response,
    setInterval,
    setTimeout,
    structuredClone,
    SubtleCrypto,
    TextDecoder,
    TextDecoderStream,
    TextEncoder,
    TextEncoderStream,
    TransformStream,
    URL,
    URLPattern,
    URLSearchParams,
    WebSocket,
    WritableStream,
    WritableStreamDefaultWriter,
  }
}
