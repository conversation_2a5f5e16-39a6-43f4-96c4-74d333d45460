/**
 * This is a compatibility ruleset that:
 * - disables rules from eslint:recommended which are already handled by TypeScript.
 * - enables rules that make sense due to TS's typechecking / transpilation.
 */
declare const _default: {
    overrides: {
        files: string[];
        rules: Record<string, "error" | "off" | "warn">;
    }[];
};
export = _default;
//# sourceMappingURL=eslint-recommended.d.ts.map