import type { ParserServicesWithTypeInformation, TSESTree } from '@typescript-eslint/utils';
import type { RuleContext } from '@typescript-eslint/utils/ts-eslint';
export declare function isArrayMethodCallWithPredicate(context: RuleContext<string, unknown[]>, services: ParserServicesWithTypeInformation, node: TSESTree.CallExpression): boolean;
//# sourceMappingURL=isArrayMethodCallWithPredicate.d.ts.map